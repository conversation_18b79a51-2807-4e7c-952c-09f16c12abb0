[train]
save_data = true               # 是否保存训练数据数据
freq = "30s"                   # 数据采样频率
min_data_length = 512          # 最小数据长度要求
split_ratio = [0.8, 0.1, 0.1]  # 训练集、验证集、测试集的划分比例 (增加训练集比例)
normalize_method = "z-score"   # 数据归一化方法 (改为z-score，更适合温度数据)
input_len = 64                 # 输入序列长度(回看窗口) (减少序列长度，提高训练效率)
output_len = 32                # 预测序列长度(预测步长)
rolling_len = 32               # 滚动预测长度
batch_size = 128               # 批处理大小 (增加批次大小，提高训练稳定性)
epochs = 200                   # 训练轮数 (减少训练轮数，避免过拟合)
accelerator = "auto"           # 自动选择加速器（CPU/GPU）
devices = "auto"               # 自动选择设备数量
gradient_clip_val = 0.5        # 梯度裁剪值，防止梯度爆炸 (减小梯度裁剪值)
early_stopping_patience = 15   # 早停patience，连续多少轮无改善后停止训练 (增加patience)

[lstm]
hidden_size = 256              # 隐藏层大小 (减小隐藏层大小)
num_layers = 2                 # 网络层数 (保持较浅的网络)
dropout = 0.15                 # Dropout比率，用于防止过拟合 (适度增加dropout)
bidirectional = false          # 是否使用双向网络
learning_rate = 2e-3           # 学习率 (增加学习率)
weight_decay = 1e-4            # 权重衰减，用于正则化 (增加正则化)
use_attention = true           # 是否使用注意力机制
use_residual = true            # 是否使用残差连接
loss_type = "mse"              # 损失函数类型: mse, huber, smooth_l1
scheduler_type = "plateau"     # 学习率调度器类型: plateau, cosine

[lstm_stm]
hidden_size = 128              # 隐藏层大小 (大幅减小，避免过拟合)
num_layers = 2                 # 网络层数 (减少层数)
dropout = 0.2                  # Dropout比率，用于防止过拟合
bidirectional = false          # 是否使用双向网络 (改为单向，适合实时预测)
learning_rate = 1e-3           # 学习率 (增加学习率)
weight_decay = 1e-4            # 权重衰减，用于正则化
use_attention = true           # 是否使用注意力机制
use_residual = true            # 是否使用残差连接
loss_type = "mse"              # 损失函数类型
scheduler_type = "plateau"     # 学习率调度器类型

[lstm_ftm]
hidden_size = 128             # 隐藏层大小 (减小隐藏层大小)
num_layers = 2                 # 网络层数
dropout = 0.15                 # Dropout比率，用于防止过拟合
bidirectional = false          # 是否使用双向网络
learning_rate = 2e-3           # 学习率 (大幅增加学习率)
weight_decay = 1e-4            # 权重衰减，用于正则化
use_attention = true           # 是否使用注意力机制
use_residual = true            # 是否使用残差连接
loss_type = "mse"              # 损失函数类型
scheduler_type = "plateau"     # 学习率调度器类型

[seq2seq]
hidden_size = 256             # 隐藏层大小 (减小隐藏层大小)
num_layers = 2                 # 网络层数
dropout = 0.2                 # Dropout比率，用于防止过拟合
bidirectional = false          # 是否使用双向网络
learning_rate = 2e-3           # 学习率 (大幅增加学习率)
weight_decay = 1e-4            # 权重衰减，用于正则化
loss_type = "mse"              # 损失函数类型
scheduler_type = "plateau"     # 学习率调度器类型

[lstm_sta]
lstm_input_size = 20           # LSTM输入特征的维度（与input_columns数量一致）
lstm_hidden_size = 256         # LSTM隐藏层的维度 (减小隐藏层大小)
dropout = 0.15                 # Dropout比率，用于防止过拟合
learning_rate = 1e-3           # 学习率
weight_decay = 1e-4            # 权重衰减，用于正则化

[furnace]
total_length = 171.1  # 炉体总长, m
ph_length = 23.08
nof_length = 30.42
rtf_length = 37.7
sf_length = 35.48
jcf_length = 44.42
weld1_max = 1200  # 焊缝位置1（距焊机）最大值
weld2_max = 518  # 焊缝位置2（距入炉密封辊）最大值

[influxdb]
host = "***********"
port = 8086
username = "root"
password = "123456"
database = "annealing_furnace"

[logging]
log_dir = "logs"
level = "INFO"
format = "[%(asctime)s] - [%(name)s] - [%(levelname)s] - %(message)s"
date_format = "%Y-%m-%d %H:%M:%S"
file_handler = true
file_path = "logs/app.log"
console_handler = true
max_days = 30

# 新增数据预处理配置
[preprocessing]
# 异常值检测配置
outlier_detection = true
outlier_method = "iqr"         # iqr, zscore, isolation_forest
outlier_threshold = 3.0        # 异常值阈值

# 特征工程配置
feature_engineering = true
add_time_features = true       # 添加时间特征（小时、星期等）
add_lag_features = true        # 添加滞后特征
lag_periods = [1, 2, 3, 5, 10] # 滞后周期
add_rolling_features = true    # 添加滚动统计特征
rolling_windows = [5, 10, 20]  # 滚动窗口大小
add_diff_features = true       # 添加差分特征

# 数据平滑配置
smoothing = true
smoothing_method = "savgol"    # savgol, moving_average, exponential
smoothing_window = 5           # 平滑窗口大小

# 温度数据特定配置
[temperature_thresholds]
# 更合理的温度阈值设置
speed_min = 5.0                # 最小速度 (降低阈值)
strip_temp_nof_min = 400.0     # NOF段钢带温度最小值 (降低阈值)
strip_temp_nof_max = 800.0     # NOF段钢带温度最大值
strip_temp_rtf_min = 400.0     # RTF段钢带温度最小值
strip_temp_rtf_max = 750.0     # RTF段钢带温度最大值
strip_temp_sf_min = 350.0      # SF段钢带温度最小值 (降低阈值)
strip_temp_sf_max = 650.0      # SF段钢带温度最大值

# 炉温阈值
temp_ph_min = 500.0            # PH段炉温最小值
temp_ph_max = 900.0            # PH段炉温最大值
temp_nof_min = 600.0           # NOF段炉温最小值
temp_nof_max = 950.0           # NOF段炉温最大值
