import torch
import torch.nn as nn


class Encoder(nn.Module):
    def __init__(self, num_past_features, hidden_dim, num_layers, dropout_rate):
        """
        编码器初始化
        :param num_past_features: 过去输入的特征数量
        :param hidden_dim: LSTM隐藏层的维度
        :param num_layers: LSTM的层数
        :param dropout_rate: Dropout比率
        """
        super(Encoder, self).__init__()
        self.lstm = nn.LSTM(
            input_size=num_past_features,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,  # 输入形状为 (batch, seq, feature)
            dropout=dropout_rate if num_layers > 1 else 0,
        )

    def forward(self, x_past):
        """
        编码器前向传播
        :param x_past: 过去的数据，形状为 [batch_size, PAST_SEQ_LEN, num_past_features]
        :return: LSTM的最后一个隐藏状态和细胞状态
        """
        # 我们只需要最后的隐藏状态和细胞状态，所以忽略LSTM的输出 `outputs`
        # `_` 是一个通用的占位符，表示我们不关心这个返回值
        _, (hidden, cell) = self.lstm(x_past)

        # hidden 的形状: [num_layers, batch_size, hidden_dim]
        # cell 的形状:   [num_layers, batch_size, hidden_dim]
        return hidden, cell


class Decoder(nn.Module):
    def __init__(
        self,
        num_future_features,
        num_target_features,
        hidden_dim,
        num_layers,
        dropout_rate,
    ):
        """
        解码器初始化
        :param num_future_features: 未来输入的特征数量
        :param num_target_features: 输出目标变量的数量
        :param hidden_dim: LSTM隐藏层的维度
        :param num_layers: LSTM的层数
        :param dropout_rate: Dropout比率
        """
        super(Decoder, self).__init__()
        self.lstm = nn.LSTM(
            input_size=num_future_features,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,  # 输入形状为 (batch, seq, feature)
            dropout=dropout_rate if num_layers > 1 else 0,
        )
        # 全连接层，将LSTM的输出映射到我们想要的目标变量维度
        self.fc = nn.Linear(hidden_dim, num_target_features)

    def forward(self, x_future, hidden, cell):
        """
        解码器前向传播
        :param x_future: 未来的已知数据，形状为 [batch_size, FUTURE_SEQ_LEN, num_future_features]
        :param hidden: 编码器传来的隐藏状态
        :param cell: 编码器传来的细胞状态
        :return: 对未来目标变量的预测，形状为 [batch_size, FUTURE_SEQ_LEN, num_target_features]
        """
        # 将编码器的状态作为解码器的初始状态
        # lstm_output 的形状: [batch_size, FUTURE_SEQ_LEN, hidden_dim]
        lstm_output, _ = self.lstm(x_future, (hidden, cell))

        # 将LSTM的每个时间步的输出都通过全连接层，得到最终预测
        # predictions 的形状: [batch_size, FUTURE_SEQ_LEN, num_target_features]
        predictions = self.fc(lstm_output)

        return predictions


class Seq2Seq(nn.Module):
    def __init__(self, encoder, decoder):
        """
        主模型初始化
        :param encoder: 编码器实例
        :param decoder: 解码器实例
        """
        super(Seq2Seq, self).__init__()
        self.encoder = encoder
        self.decoder = decoder

    def forward(self, x_past, x_future):
        """
        主模型前向传播
        :param x_past: 过去的数据，形状为 [batch_size, PAST_SEQ_LEN, num_past_features]
        :param x_future: 未来的已知数据，形状为 [batch_size, FUTURE_SEQ_LEN, num_future_features]
        :return: 最终的预测结果
        """
        # 1. 使用编码器处理过去的数据，得到上下文向量
        hidden, cell = self.encoder(x_past)

        # 2. 使用解码器和上下文向量来预测未来
        predictions = self.decoder(x_future, hidden, cell)

        return predictions
