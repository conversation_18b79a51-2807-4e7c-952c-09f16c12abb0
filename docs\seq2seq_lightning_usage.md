# Seq2Seq Lightning 模型使用指南

## 概述

本文档介绍如何使用基于 PyTorch Lightning 框架的 Seq2Seq 模型，包括基础的 `LitSeq2Seq` 和带注意力机制的 `LitSeq2SeqWithAttention`。

## 模型特性

### LitSeq2Seq
- **编码器-解码器架构**：使用 LSTM 处理序列到序列的映射
- **完整的Lightning集成**：支持自动训练、验证、测试流程
- **多种损失函数**：支持 MSE、Huber、SmoothL1 损失
- **学习率调度**：支持 ReduceLROnPlateau 和 CosineAnnealingLR

### LitSeq2SeqWithAttention
- **注意力机制**：在解码过程中动态关注编码器的不同时间步
- **注意力权重可视化**：提供 `get_attention_weights()` 方法
- **更强的表达能力**：适用于复杂的序列预测任务

## 快速开始

### 1. 基础 Seq2Seq 模型使用

```python
import torch
import lightning as L
from src.model.lit_seq2seq import LitSeq2Seq

# 模型参数
num_past_features = 10      # 过去数据的特征数
num_future_features = 5     # 未来已知数据的特征数
num_target_features = 1     # 目标变量的特征数
hidden_dim = 128           # LSTM隐藏层维度
num_layers = 2             # LSTM层数

# 创建模型
model = LitSeq2Seq(
    num_past_features=num_past_features,
    num_future_features=num_future_features,
    num_target_features=num_target_features,
    hidden_dim=hidden_dim,
    num_layers=num_layers,
    dropout_rate=0.1,
    learning_rate=1e-3,
    weight_decay=1e-5,
    loss_type="mse",
    scheduler_type="plateau"
)

# 创建示例数据
batch_size = 32
past_seq_len = 20
future_seq_len = 10

x_past = torch.randn(batch_size, past_seq_len, num_past_features)
x_future = torch.randn(batch_size, future_seq_len, num_future_features)
y = torch.randn(batch_size, future_seq_len, num_target_features)

# 前向传播
predictions = model(x_past, x_future)
print(f"预测结果形状: {predictions.shape}")
```

### 2. 带注意力机制的 Seq2Seq 模型使用

```python
from src.model.lit_seq2seq_attention import LitSeq2SeqWithAttention

# 创建带注意力的模型
attention_model = LitSeq2SeqWithAttention(
    num_past_features=num_past_features,
    num_future_features=num_future_features,
    num_target_features=num_target_features,
    hidden_dim=hidden_dim,
    num_layers=num_layers,
    dropout_rate=0.1,
    learning_rate=1e-3,
    weight_decay=1e-5,
    loss_type="mse",
    scheduler_type="plateau"
)

# 前向传播
predictions = attention_model(x_past, x_future)
print(f"预测结果形状: {predictions.shape}")

# 获取注意力权重
attention_weights = attention_model.get_attention_weights(x_past, x_future)
print(f"注意力权重数量: {len(attention_weights)}")
print(f"每个权重的形状: {attention_weights[0].shape}")
```

### 3. 训练模型

```python
import lightning as L
from torch.utils.data import DataLoader, TensorDataset

# 准备数据
dataset = TensorDataset(x_past, x_future, y)
train_loader = DataLoader(dataset, batch_size=32, shuffle=True)
val_loader = DataLoader(dataset, batch_size=32, shuffle=False)

# 创建训练器
trainer = L.Trainer(
    max_epochs=100,
    accelerator="auto",
    devices="auto",
    precision="16-mixed",  # 混合精度训练
    log_every_n_steps=10,
)

# 训练模型
trainer.fit(model, train_loader, val_loader)
```

## 数据格式要求

### 输入数据格式
- `x_past`: 过去的数据，形状为 `[batch_size, past_seq_len, num_past_features]`
- `x_future`: 未来的已知数据，形状为 `[batch_size, future_seq_len, num_future_features]`
- `y`: 目标数据，形状为 `[batch_size, future_seq_len, num_target_features]`

### 训练批次格式
训练时，每个批次应该是一个包含三个张量的元组：`(x_past, x_future, y)`

## 模型参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `num_past_features` | int | - | 过去输入的特征数量 |
| `num_future_features` | int | - | 未来输入的特征数量 |
| `num_target_features` | int | - | 输出目标变量的数量 |
| `hidden_dim` | int | 128 | LSTM隐藏层的维度 |
| `num_layers` | int | 2 | LSTM的层数 |
| `dropout_rate` | float | 0.1 | Dropout比率 |
| `learning_rate` | float | 1e-3 | 学习率 |
| `weight_decay` | float | 1e-5 | 权重衰减系数 |
| `loss_type` | str | "mse" | 损失函数类型 ("mse", "huber", "smooth_l1") |
| `scheduler_type` | str | "plateau" | 学习率调度器类型 ("plateau", "cosine") |

## 注意力权重可视化

对于 `LitSeq2SeqWithAttention` 模型，可以使用以下代码可视化注意力权重：

```python
import matplotlib.pyplot as plt
import numpy as np

# 获取注意力权重
attention_weights = attention_model.get_attention_weights(x_past, x_future)

# 可视化第一个样本的注意力权重
sample_idx = 0
attention_matrix = torch.stack([w[sample_idx] for w in attention_weights])

plt.figure(figsize=(12, 8))
plt.imshow(attention_matrix.cpu().numpy(), cmap='Blues', aspect='auto')
plt.xlabel('过去时间步')
plt.ylabel('未来时间步')
plt.title('注意力权重热力图')
plt.colorbar()
plt.show()
```

## 最佳实践

1. **数据预处理**：确保输入数据已经标准化或归一化
2. **批次大小**：根据GPU内存调整批次大小，通常32-128效果较好
3. **学习率**：从1e-3开始，根据训练效果调整
4. **早停**：使用验证损失进行早停，避免过拟合
5. **注意力模型**：对于复杂序列关系，优先考虑使用注意力机制

## 故障排除

### 常见问题

1. **内存不足**：减小批次大小或隐藏层维度
2. **训练不收敛**：降低学习率或增加正则化
3. **过拟合**：增加dropout率或减少模型复杂度
4. **注意力权重异常**：检查输入数据的尺度和分布

### 调试技巧

```python
# 检查模型输出形状
print(f"模型输出形状: {predictions.shape}")
print(f"目标数据形状: {y.shape}")

# 检查损失值
loss = model.criterion(predictions, y)
print(f"损失值: {loss.item()}")

# 检查梯度
for name, param in model.named_parameters():
    if param.grad is not None:
        print(f"{name}: {param.grad.norm().item()}")
```
