import enum


class FileNameEnum(enum.Enum):
    TRACK_HIS = "data_track_history"
    TRACKED = "data_tracked"
    SOURCE = "data_source"
    SORTED = "data_sorted"
    INDEXED = "data_indexed"
    ANOMALY_REMOVED = "data_anomaly_removed"
    FILLED = "data_filled"
    RESAMPLED = "data_resampled"
    FINAL = "data_final"
    SEGMENT = "data_segment"
    TRAIN = "data_train"
    VALID = "data_valid"
    TEST = "data_test"

    # meta
    NORMALIZE_META = "normalize_meta"
    TRAIN_CONFIG_META = "train_config_meta"
    MODEL_CONFIG_META = "model_config_meta"
    STEEL_GRADE_META = "steel_grade_meta"

    # model name
    LSTM_NAME = "lstm_model"
    FTM_NAME = "furnace_temp_model"
    STM_NAME = "strip_temp_model"
    SEQ2SEQ_NAME = "seq2seq"
