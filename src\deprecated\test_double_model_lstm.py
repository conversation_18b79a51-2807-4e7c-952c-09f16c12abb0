import os
import logging
import lightning as L

from datetime import datetime
from config import Configs, Logger
from config.configs import TrainConfig, LSTMConfig
from data_loader import LSTMDataLoader
from model.lstm import LitLSTM
from lightning.pytorch.callbacks import (
    ModelCheckpoint,
    EarlyStopping,
    LearningRateMonitor,
)
from lightning.pytorch.loggers import TensorBoardLogger
from enums import FileNameEnum as fne

MODEL = "LSTM"
MODEL_NAMES = [fne.STM_NAME.value, fne.FTM_NAME.value]


def test():
    Configs.initialize()
    Logger.initialize()

    train_config = Configs.get_train_config()

    start_time = datetime.strptime("2025-05-01", "%Y-%m-%d")
    end_time = datetime.strptime("2025-06-30", "%Y-%m-%d")

    work_dir = f"data/test-{MODEL.lower()}/{datetime.now().strftime("%Y%m%d")}"
    # work_dir = f"data/test-{MODEL.lower()}/"
    data_loader = LSTMDataLoader(
        start_time=start_time, end_time=end_time, work_dir=work_dir
    )
    # data_loader.prepare_data()
    data_loader.setup()

    for model_name in MODEL_NAMES:
        train_model(
            model_name=model_name,
            work_dir=work_dir,
            data_loader=data_loader,
            train_config=train_config,
        )

    logging.info("训练完成")


def train_model(
    model_name: str,
    work_dir: str,
    data_loader: LSTMDataLoader,
    train_config: TrainConfig,
):

    if model_name == fne.FTM_NAME.value:
        lstm_config = Configs.get_ftm_config()
        input_size = len(train_config.input_columns_ftm)
        output_size = len(train_config.target_columns_ftm)
        train_loader = data_loader.train_dataloader_ftm()
        val_loader = data_loader.val_dataloader_ftm()
        test_loader = data_loader.test_dataloader_ftm()

    elif model_name == fne.STM_NAME.value:
        lstm_config = Configs.get_stm_config()
        input_size = len(train_config.input_columns_stm)
        output_size = len(train_config.target_columns_stm)
        train_loader = data_loader.train_dataloader_stm()
        val_loader = data_loader.val_dataloader_stm()
        test_loader = data_loader.test_dataloader_stm()
    else:
        raise KeyError(f"model_name error: {model_name}")

    model = LitLSTM(
        input_size=input_size,
        hidden_size=lstm_config.hidden_size,
        num_layers=lstm_config.num_layers,
        output_size=output_size,
        dropout=lstm_config.dropout,
        bidirectional=lstm_config.bidirectional,
        learning_rate=lstm_config.learning_rate,
        weight_decay=lstm_config.weight_decay,
        use_attention=lstm_config.use_attention,
        use_residual=lstm_config.use_residual,
        loss_type=lstm_config.loss_type,
        scheduler_type=lstm_config.scheduler_type,
    )

    # 设置回调
    checkpoint_callback = get_checkpoint_callback(model_name)
    early_stopping = get_early_stopping_callback(train_config.early_stopping_patience)
    lr_monitor = get_lr_monitor()
    # 设置日志记录器
    tensorboard_logger = get_tensorboard_logger(model_name)

    # 创建训练器
    trainer = L.Trainer(
        max_epochs=train_config.epochs,
        callbacks=[checkpoint_callback, early_stopping, lr_monitor],
        logger=tensorboard_logger,
        accelerator=train_config.accelerator,
        devices=train_config.devices,
        gradient_clip_val=train_config.gradient_clip_val,
        precision="16-mixed",  # 启用混合精度训练
        deterministic=True,  # 确保结果可重现
        enable_progress_bar=True,  # 显示进度条
    )

    # 训练模型
    trainer.fit(
        model=model,
        train_dataloaders=train_loader,
        val_dataloaders=val_loader,
    )

    # 评估模型
    trainer.test(model=model, dataloaders=test_loader)

    # 保存模型
    save_path = os.path.join(work_dir, f"{model_name.lower()}.ckpt")
    trainer.save_checkpoint(save_path)


def get_checkpoint_callback(model_name: str):
    return ModelCheckpoint(
        monitor="val_loss",
        filename=f"{model_name.lower()}-{{epoch:02d}}-{{val_loss:.4f}}",
        save_top_k=3,
        mode="min",
    )


def get_early_stopping_callback(patience: int = 10):
    return EarlyStopping(
        monitor="val_loss",
        patience=patience,
        mode="min",
    )


def get_lr_monitor():
    return LearningRateMonitor(logging_interval="epoch")


def get_tensorboard_logger(model_name: str):
    return TensorBoardLogger("logs", name=model_name.lower())


if __name__ == "__main__":
    test()
