from dataclasses import dataclass
import logging

import numpy as np

_logger = logging.getLogger(__name__)


@dataclass
class Metrics:
    n: int = 0
    MAE: float = np.nan
    MSE: float = np.nan
    RMSE: float = np.nan
    R2: float = np.nan
    MAPE: float = np.nan
    SMAPE: float = np.nan

    def to_str(self) -> str:
        return f"样本数量: {self.n}, MAE: {self.MAE:.4f}, MSE: {self.MSE:.4f}, RMSE: {self.RMSE:.4f}, R2: {self.R2:.4f}, MAPE: {self.MAPE:.2f}%, SMAPE: {self.SMAPE:.2f}%"


def calculate_metrics(y_true, y_pred, epsilon=1e-10) -> Metrics:
    """计算评估指标"""
    try:
        y_true = np.asarray(y_true, dtype=np.float64).flatten()
        y_pred = np.asarray(y_pred, dtype=np.float64).flatten()

        # 检查形状是否相同
        if y_true.shape != y_pred.shape:
            raise ValueError("输入数组的形状必须相同")
        if len(y_true) == 0:
            raise ValueError("输入数组不能为空")

        # 样本数量
        n = len(y_true)

        # 1. MAE (Mean Absolute Error) - 平均绝对误差
        # 意义: 预测值与真实值绝对误差的平均值。对异常值不敏感，值越小越好。
        # 公式: MAE = (1/n) * Σ|y_true - y_pred|
        mae = np.mean(np.abs(y_true - y_pred))

        # 2. MSE (Mean Squared Error) - 均方误差
        # 意义: 预测值与真实值误差平方的平均值。放大较大误差，对异常值敏感。
        # 公式: MSE = (1/n) * Σ(y_true - y_pred)^2
        mse = np.mean((y_true - y_pred) ** 2)

        # 3. RMSE (Root Mean Squared Error) - 均方根误差
        # 意义: MSE的平方根。保持与原始数据相同的量纲，更易解释。
        # 公式: RMSE = √MSE
        rmse = np.sqrt(mse)

        # 4. R² (Coefficient of Determination) - 决定系数
        # 意义: 模型解释的方差比例。[0,1]区间，1表示完美拟合，0表示不如均值预测。
        # 公式: R² = 1 - SS_res/SS_tot
        r2 = np.nan
        if n >= 2:
            ss_res = np.sum((y_true - y_pred) ** 2)  # 残差平方和
            ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)  # 总平方和
            r2 = 1 - (ss_res / (ss_tot + epsilon))  # 添加小常数防止除零

        # 5. MAPE (Mean Absolute Percentage Error) - 平均绝对百分比误差
        # 意义: 相对误差的绝对值平均值。直观百分比误差，但对零值和负值敏感。
        # 公式: MAPE = (1/n) * Σ|(y_true - y_pred)/y_true| * 100%
        mape = np.mean(np.abs((y_true - y_pred) / (np.abs(y_true) + epsilon))) * 100

        # 6. SMAPE (Symmetric Mean Absolute Percentage Error) - 对称平均绝对百分比误差
        # 意义: 对称的相对误差度量，[0%, 200%]区间，处理正负误差不对称问题。
        # 公式: SMAPE = (1/n) * Σ[2|y_pred-y_true|/(|y_true|+|y_pred|)] * 100%
        smape = (
            np.mean(
                2
                * np.abs(y_pred - y_true)
                / (np.abs(y_true) + np.abs(y_pred) + epsilon)
            )
            * 100
        )

        return Metrics(n, float(mae), float(mse), rmse, r2, float(mape), float(smape))
    except Exception as e:
        _logger.error(f"计算指标失败: {e}")
        return Metrics()
