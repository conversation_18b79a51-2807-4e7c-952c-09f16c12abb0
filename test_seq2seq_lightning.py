#!/usr/bin/env python3
"""
测试 Seq2Seq Lightning 模型的脚本
"""

import torch
import torch.nn as nn
import lightning as L
from torch.utils.data import DataLoader, TensorDataset

# 导入我们创建的模型
from src.model.lit_seq2seq import LitSeq2Seq
from src.model.lit_seq2seq_attention import LitSeq2SeqWithAttention


def test_basic_functionality():
    """测试模型的基本功能"""
    print("=" * 50)
    print("测试模型基本功能")
    print("=" * 50)
    
    # 模型参数
    num_past_features = 10
    num_future_features = 5
    num_target_features = 1
    hidden_dim = 64
    num_layers = 2
    batch_size = 8
    past_seq_len = 20
    future_seq_len = 10
    
    # 创建测试数据
    x_past = torch.randn(batch_size, past_seq_len, num_past_features)
    x_future = torch.randn(batch_size, future_seq_len, num_future_features)
    y = torch.randn(batch_size, future_seq_len, num_target_features)
    
    print(f"输入数据形状:")
    print(f"  x_past: {x_past.shape}")
    print(f"  x_future: {x_future.shape}")
    print(f"  y: {y.shape}")
    
    # 测试基础 Seq2Seq 模型
    print("\n1. 测试基础 Seq2Seq 模型")
    model = LitSeq2Seq(
        num_past_features=num_past_features,
        num_future_features=num_future_features,
        num_target_features=num_target_features,
        hidden_dim=hidden_dim,
        num_layers=num_layers,
        dropout_rate=0.1,
        learning_rate=1e-3,
    )
    
    # 前向传播
    predictions = model(x_past, x_future)
    print(f"  预测结果形状: {predictions.shape}")
    
    # 计算损失
    loss = model.criterion(predictions, y)
    print(f"  损失值: {loss.item():.4f}")
    
    # 测试训练步骤
    batch = (x_past, x_future, y)
    train_result = model.training_step(batch, 0)
    print(f"  训练步骤损失: {train_result['loss'].item():.4f}")
    
    # 测试验证步骤
    val_result = model.validation_step(batch, 0)
    print(f"  验证步骤损失: {val_result['val_loss'].item():.4f}")
    print(f"  验证MAE: {val_result['val_mae'].item():.4f}")
    print(f"  验证RMSE: {val_result['val_rmse'].item():.4f}")
    
    # 测试带注意力的 Seq2Seq 模型
    print("\n2. 测试带注意力的 Seq2Seq 模型")
    attention_model = LitSeq2SeqWithAttention(
        num_past_features=num_past_features,
        num_future_features=num_future_features,
        num_target_features=num_target_features,
        hidden_dim=hidden_dim,
        num_layers=num_layers,
        dropout_rate=0.1,
        learning_rate=1e-3,
    )
    
    # 前向传播
    attention_predictions = attention_model(x_past, x_future)
    print(f"  预测结果形状: {attention_predictions.shape}")
    
    # 计算损失
    attention_loss = attention_model.criterion(attention_predictions, y)
    print(f"  损失值: {attention_loss.item():.4f}")
    
    # 测试注意力权重
    attention_weights = attention_model.get_attention_weights(x_past, x_future)
    print(f"  注意力权重数量: {len(attention_weights)}")
    print(f"  每个权重的形状: {attention_weights[0].shape}")
    
    print("\n✅ 基本功能测试通过!")


def test_optimizer_configuration():
    """测试优化器配置"""
    print("\n" + "=" * 50)
    print("测试优化器配置")
    print("=" * 50)
    
    # 创建模型
    model = LitSeq2Seq(
        num_past_features=10,
        num_future_features=5,
        num_target_features=1,
        hidden_dim=32,
        num_layers=1,
        scheduler_type="plateau"
    )
    
    # 测试优化器配置
    optimizer_config = model.configure_optimizers()
    print(f"优化器类型: {type(optimizer_config['optimizer'])}")
    print(f"调度器类型: {type(optimizer_config['lr_scheduler']['scheduler'])}")
    print(f"监控指标: {optimizer_config['lr_scheduler']['monitor']}")
    
    # 测试余弦退火调度器
    model_cosine = LitSeq2Seq(
        num_past_features=10,
        num_future_features=5,
        num_target_features=1,
        hidden_dim=32,
        num_layers=1,
        scheduler_type="cosine"
    )
    
    optimizer_config_cosine = model_cosine.configure_optimizers()
    print(f"余弦调度器类型: {type(optimizer_config_cosine['lr_scheduler']['scheduler'])}")
    
    print("\n✅ 优化器配置测试通过!")


def test_different_loss_functions():
    """测试不同的损失函数"""
    print("\n" + "=" * 50)
    print("测试不同损失函数")
    print("=" * 50)
    
    # 创建测试数据
    x_past = torch.randn(4, 10, 5)
    x_future = torch.randn(4, 5, 3)
    y = torch.randn(4, 5, 1)
    
    loss_types = ["mse", "huber", "smooth_l1"]
    
    for loss_type in loss_types:
        model = LitSeq2Seq(
            num_past_features=5,
            num_future_features=3,
            num_target_features=1,
            hidden_dim=32,
            num_layers=1,
            loss_type=loss_type
        )
        
        predictions = model(x_past, x_future)
        loss = model.criterion(predictions, y)
        print(f"  {loss_type.upper()} 损失: {loss.item():.4f}")
    
    print("\n✅ 损失函数测试通过!")


def test_model_parameters():
    """测试模型参数数量"""
    print("\n" + "=" * 50)
    print("测试模型参数")
    print("=" * 50)
    
    # 基础模型
    model = LitSeq2Seq(
        num_past_features=10,
        num_future_features=5,
        num_target_features=1,
        hidden_dim=64,
        num_layers=2,
    )
    
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"基础 Seq2Seq 模型:")
    print(f"  总参数数量: {total_params:,}")
    print(f"  可训练参数数量: {trainable_params:,}")
    
    # 注意力模型
    attention_model = LitSeq2SeqWithAttention(
        num_past_features=10,
        num_future_features=5,
        num_target_features=1,
        hidden_dim=64,
        num_layers=2,
    )
    
    attention_total_params = sum(p.numel() for p in attention_model.parameters())
    attention_trainable_params = sum(p.numel() for p in attention_model.parameters() if p.requires_grad)
    
    print(f"\n带注意力的 Seq2Seq 模型:")
    print(f"  总参数数量: {attention_total_params:,}")
    print(f"  可训练参数数量: {attention_trainable_params:,}")
    
    print(f"\n注意力机制增加的参数: {attention_total_params - total_params:,}")
    
    print("\n✅ 模型参数测试通过!")


def main():
    """主测试函数"""
    print("开始测试 Seq2Seq Lightning 模型")
    
    try:
        test_basic_functionality()
        test_optimizer_configuration()
        test_different_loss_functions()
        test_model_parameters()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试通过!")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
