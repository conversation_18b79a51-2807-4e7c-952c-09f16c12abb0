import os

import lightning as L

from datetime import datetime
from config import Configs, Logger
from data_loader import AFLoader
from model.lstm import LitLSTM
from lightning.pytorch.callbacks import (
    ModelCheckpoint,
    EarlyStopping,
    LearningRateMonitor,
)
from lightning.pytorch.loggers import TensorBoardLogger

MODEL_NAME = "LSTM"


def test_af_loader():
    Configs.initialize()
    Logger.initialize()

    train_config = Configs.get_train_config()
    lstm_config = Configs.get_lstm_config()

    start_time = datetime.strptime("2025-05-01", "%Y-%m-%d")
    end_time = datetime.strptime("2025-06-30", "%Y-%m-%d")

    # work_dir = f"data/test/{datetime.now().strftime("%Y%m%d")}"
    work_dir = f"data/test-{MODEL_NAME.lower()}/"
    data_loader = AFLoader(start_time=start_time, end_time=end_time, work_dir=work_dir)
    # data_loader.prepare_data()
    data_loader.setup()

    train_loader = data_loader.train_dataloader()
    batch = next(iter(train_loader))
    x, y = batch
    input_size = x.shape[-1]
    output_size = y.shape[-1]
    print(
        f"输入特征维度: {input_size}, 输出特征维度: {output_size}, 数据批次形状 - 输入: {x.shape}, 输出: {y.shape}"
    )

    model = LitLSTM(
        input_size=input_size,
        hidden_size=lstm_config.hidden_size,
        num_layers=lstm_config.num_layers,
        output_size=output_size,
        dropout=lstm_config.dropout,
        bidirectional=lstm_config.bidirectional,
        learning_rate=lstm_config.learning_rate,
        weight_decay=lstm_config.weight_decay,
    )

    # 设置回调
    checkpoint_callback = ModelCheckpoint(
        monitor="val_loss",
        filename="lstm-{epoch:02d}-{val_loss:.4f}",
        save_top_k=3,
        mode="min",
    )

    early_stopping = EarlyStopping(
        monitor="val_loss",
        patience=train_config.early_stopping_patience,
        mode="min",
    )

    lr_monitor = LearningRateMonitor(logging_interval="epoch")

    # 设置日志记录器
    tensorboard_logger = TensorBoardLogger("logs", name="lstm")

    # 创建训练器
    trainer = L.Trainer(
        max_epochs=train_config.epochs,
        callbacks=[checkpoint_callback, early_stopping, lr_monitor],
        logger=tensorboard_logger,
        accelerator=train_config.accelerator,
        devices=train_config.devices,
        gradient_clip_val=train_config.gradient_clip_val,
    )

    # 训练模型
    trainer.fit(
        model=model,
        train_dataloaders=data_loader.train_dataloader(),
        val_dataloaders=data_loader.val_dataloader(),
    )

    # 评估模型
    trainer.test(model=model, dataloaders=data_loader.test_dataloader())

    # 保存模型
    save_path = os.path.join(work_dir, "model.ckpt")
    trainer.save_checkpoint(save_path)
    print(f"模型已保存到 {save_path}")


if __name__ == "__main__":
    test_af_loader()
