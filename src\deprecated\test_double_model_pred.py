from datetime import datetime
import glob
import json
import logging
from pathlib import Path
import random
from typing import Any, List, Optional, Tuple

import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import matplotlib
import seaborn as sns
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

from config import Configs, Logger
from enums.file_name import FileNameEnum as fne
from model.lstm import LitLSTM
from model.gru import LitGRU
from utils.data_utils import denormalize, normalize_by_params

MODEL_DIR = f"./data/test-lstm/{datetime.now().strftime("%Y%m%d")}"
# MODEL_DIR = f"./data/test-lstm/20250728"
DATA_FILE_NAME = "data_test_*"

_logger = logging.getLogger(__name__)


class MultiModelPredictor:

    def __init__(
        self, model_dir: str = MODEL_DIR, data_file_name: str = DATA_FILE_NAME
    ):
        self.model_dir = Path(model_dir)
        self.meta_dir = self.model_dir / "meta"
        self.data_dir = self.model_dir / "data"
        self.visual_dir = self.model_dir / "visualizations"
        self.data_file_name = data_file_name

        self.train_config = Configs.get_train_config()
        ftm_config = Configs.get_ftm_config()
        stm_cofnig = Configs.get_stm_config()

        # ftm
        self.ftm_input_columns = self.train_config.input_columns_ftm
        self.ftm_target_columns = self.train_config.target_columns_ftm
        self.ftm_non_target_columns = [
            column
            for column in self.ftm_input_columns
            if column not in self.ftm_target_columns
        ]
        # stm
        self.stm_input_columns = self.train_config.input_columns_stm
        self.stm_target_columns = self.train_config.target_columns_stm
        self.stm_non_target_columns = [
            column
            for column in self.stm_input_columns
            if column not in self.stm_target_columns
        ]

        # 合并并去重non_target_columns，避免重复字段
        self.all_non_target_columns = list(
            set(self.ftm_non_target_columns + self.stm_non_target_columns)
        )

        self.ftm_model = None
        self.stm_model = None
        self.normalize_meta = None
        self.ftm_target_normalize_meta = None
        self.stm_target_normalize_meta = None

        self.seq_length = self.train_config.input_len
        self.output_length = self.train_config.output_len
        self.rolling_steps = self.train_config.rolling_len

    def load_model(self) -> None:
        try:
            # 加载归一化数据
            meta_file = self.meta_dir / f"{fne.NORMALIZE_META.value}.json"
            if not meta_file.exists():
                raise FileNotFoundError(f"元数据文件不存在: {meta_file}")

            with open(meta_file, "r", encoding="utf-8") as f:
                self.normalize_meta = json.load(f)

            self.ftm_target_normalize_meta = {
                col: self.normalize_meta[col]
                for col in self.ftm_target_columns
                if col in self.normalize_meta
            }

            self.stm_target_normalize_meta = {
                col: self.normalize_meta[col]
                for col in self.stm_target_columns
                if col in self.normalize_meta
            }

            _logger.info(
                f"成功加载归一化元数据，包含 {len(self.normalize_meta)} 个特征"
            )

            # 加载模型
            model_classes = [LitLSTM, LitGRU]

            ftm_model_file = self.model_dir / f"{fne.FTM_NAME.value}.ckpt"
            if not ftm_model_file.exists():
                raise FileNotFoundError(f"模型文件不存在: {ftm_model_file}")

            stm_model_file = self.model_dir / f"{fne.STM_NAME.value}.ckpt"
            if not stm_model_file.exists():
                raise FileNotFoundError(f"模型文件不存在: {stm_model_file}")

            for model_class in model_classes:
                try:
                    # 获取FTM配置
                    ftm_config = Configs.get_ftm_config()
                    self.ftm_model = model_class.load_from_checkpoint(
                        str(ftm_model_file),
                        input_size=len(self.ftm_input_columns),
                        output_size=len(self.ftm_target_columns),
                        hidden_size=ftm_config.hidden_size,
                        num_layers=ftm_config.num_layers,
                        dropout=ftm_config.dropout,
                        bidirectional=ftm_config.bidirectional,
                        learning_rate=ftm_config.learning_rate,
                        weight_decay=ftm_config.weight_decay,
                        use_attention=ftm_config.use_attention,
                        use_residual=ftm_config.use_residual,
                        loss_type=ftm_config.loss_type,
                        scheduler_type=ftm_config.scheduler_type,
                    )
                    self.ftm_model.eval()
                    _logger.info(f"成功加载FTM模型: {model_class.__name__}")
                    break
                except Exception as e:
                    _logger.debug(f"尝试加载 {model_class.__name__} 失败: {e}")
                    continue

            for model_class in model_classes:
                try:
                    # 获取STM配置
                    stm_config = Configs.get_stm_config()
                    self.stm_model = model_class.load_from_checkpoint(
                        str(stm_model_file),
                        input_size=len(self.stm_input_columns),
                        output_size=len(self.stm_target_columns),
                        hidden_size=stm_config.hidden_size,
                        num_layers=stm_config.num_layers,
                        dropout=stm_config.dropout,
                        bidirectional=stm_config.bidirectional,
                        learning_rate=stm_config.learning_rate,
                        weight_decay=stm_config.weight_decay,
                        use_attention=stm_config.use_attention,
                        use_residual=stm_config.use_residual,
                        loss_type=stm_config.loss_type,
                        scheduler_type=stm_config.scheduler_type,
                    )
                    self.stm_model.eval()
                    _logger.info(f"成功加载STM模型: {model_class.__name__}")
                    break
                except Exception as e:
                    _logger.debug(f"尝试加载 {model_class.__name__} 失败: {e}")
                    continue

            if self.ftm_model is None:
                raise RuntimeError("无法加载FTM模型，请检查模型文件格式")
            if self.stm_model is None:
                raise RuntimeError("无法加载STM模型，请检查模型文件格式")

        except Exception as e:
            _logger.error(f"加载模型和元数据失败: {e}")
            raise

    def load_data(self, file_name: str) -> List[pd.DataFrame]:
        try:
            test_files = sorted(glob.glob(str(self.data_dir / f"{file_name}.csv")))
            if not test_files:
                raise FileNotFoundError(
                    f"在 {self.data_dir} 中未找到测试数据文件: {file_name}"
                )

            dfs = []
            for file_path in test_files:
                df = pd.read_csv(file_path)

                # 设置时间索引
                if "INDEX" in df.columns:
                    df["INDEX"] = pd.to_datetime(df["INDEX"])
                    df = df.set_index("INDEX")

                # 删除缺失值
                df = df.dropna()

                if len(df) >= self.seq_length + 1:
                    dfs.append(df)
                else:
                    _logger.warning(f"文件 {file_path} 数据量不足，跳过")

            return dfs
        except Exception as e:
            _logger.error(f"加载数据失败: {e}")
            raise

    def preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        try:
            # 检查元数据是否已加载
            if self.normalize_meta is None:
                raise ValueError("归一化元数据未加载")
            # 应用归一化
            df_normalized = normalize_by_params(df, self.normalize_meta)
            return df_normalized
        except Exception as e:
            _logger.error(f"数据预处理失败: {e}")
            raise

    def create_sequences(
        self, data: pd.DataFrame, input_cols: list[str], target_cols: list[str]
    ) -> Tuple[np.ndarray, np.ndarray]:
        try:
            sequences = []
            targets = []
            for i in range(len(data) - self.seq_length):
                seq = data.iloc[i : i + self.seq_length][input_cols].values
                tar = data.iloc[i + self.seq_length][target_cols].values
                sequences.append(seq)
                targets.append(tar)
            return np.array(sequences), np.array(targets)
        except Exception as e:
            _logger.error(f"创建序列数据失败: {e}")
            raise

    def standard_pred(self, df: pd.DataFrame):
        try:

            if self.ftm_model is None or self.stm_model is None:
                raise ValueError("模型未加载")

            df_normalized = self.preprocess_data(df)

            y_pred_ftm, y_true_ftm = self.prediction(
                df_normalized,
                self.ftm_model,
                self.ftm_input_columns,
                self.ftm_target_columns,
            )
            y_pred_stm, y_true_stm = self.prediction(
                df_normalized,
                self.stm_model,
                self.stm_input_columns,
                self.stm_target_columns,
            )

            # 反归一化预测结果和真实值
            y_pred_ftm_denorm = self.denormalize_predictions(
                y_pred_ftm, self.ftm_target_columns, self.ftm_target_normalize_meta
            )
            y_true_ftm_denorm = self.denormalize_predictions(
                y_true_ftm, self.ftm_target_columns, self.ftm_target_normalize_meta
            )
            y_pred_stm_denorm = self.denormalize_predictions(
                y_pred_stm, self.stm_target_columns, self.stm_target_normalize_meta
            )
            y_true_stm_denorm = self.denormalize_predictions(
                y_true_stm, self.stm_target_columns, self.stm_target_normalize_meta
            )

            metrics_ftm = self.calculate_metrics(
                y_true_ftm_denorm, y_pred_ftm_denorm, self.ftm_target_columns
            )
            metrics_stm = self.calculate_metrics(
                y_true_stm_denorm, y_pred_stm_denorm, self.stm_target_columns
            )

            return {
                "ftm": {
                    "predictions": y_pred_ftm_denorm,
                    "actual": y_true_ftm_denorm,
                    "metrics": metrics_ftm,
                },
                "stm": {
                    "predictions": y_pred_stm_denorm,
                    "actual": y_true_stm_denorm,
                    "metrics": metrics_stm,
                },
            }

        except Exception as e:
            _logger.error(f"标准预测失败 {e}")
            raise

    def rolling_pred(self, init_df: pd.DataFrame, next_df: pd.DataFrame):
        """
        进行滚动预测

        Args:
            init_df: 包含初步输入模型的时间窗口数据
            operate_df: 包含操作参数的时间窗口数据(未来窗口)

        Returns:
            dict: 包含滚动预测结果和评估指标的字典
        """
        try:
            # 检查模型是否已加载
            if self.ftm_model is None or self.stm_model is None:
                raise ValueError("模型未加载")

            if len(init_df) < self.seq_length:
                raise ValueError("请检查初始窗口长度")

            if len(next_df) < self.rolling_steps:
                raise ValueError("请检查操作参数长度")

            # 预处理数据
            init_df_norm = self.preprocess_data(init_df)
            next_df_norm = self.preprocess_data(next_df)

            # 存储所有滚动预测结果
            ftm_preds_denorm = []
            ftm_acts_denorm = []
            stm_preds_denorm = []
            stm_acts_denorm = []

            start_time = datetime.now()
            # 开始滚动预测
            input_norm = init_df_norm.copy()
            for i in range(self.rolling_steps):

                # 构建输入序列, 带入真实值以评估模型
                input_norm = pd.concat(
                    [
                        input_norm.tail(self.seq_length),
                        next_df_norm.iloc[i].to_frame().T,
                    ],
                    ignore_index=False,
                )

                y_pred_ftm, y_true_ftm = self.prediction(
                    input_norm,
                    self.ftm_model,
                    self.ftm_input_columns,
                    self.ftm_target_columns,
                )

                y_pred_stm, y_true_stm = self.prediction(
                    input_norm,
                    self.stm_model,
                    self.stm_input_columns,
                    self.stm_target_columns,
                )

                # 更改真实值为预测值
                input_norm.loc[input_norm.index[-1], self.ftm_target_columns] = (
                    y_pred_ftm.tolist()
                )

                input_norm.loc[input_norm.index[-1], self.stm_target_columns] = (
                    y_pred_stm
                )

                # 保存预测结果
                ftm_preds_denorm.append(
                    self.denormalize_predictions(
                        y_pred_ftm, self.ftm_target_columns, self.normalize_meta
                    )
                )
                ftm_acts_denorm.append(
                    self.denormalize_predictions(
                        y_true_ftm, self.ftm_target_columns, self.normalize_meta
                    )
                )
                stm_preds_denorm.append(
                    self.denormalize_predictions(
                        y_pred_stm, self.stm_target_columns, self.normalize_meta
                    )
                )
                stm_acts_denorm.append(
                    self.denormalize_predictions(
                        y_true_stm, self.stm_target_columns, self.normalize_meta
                    )
                )

            _logger.info(
                f"滚动预测耗时: {(datetime.now() - start_time).total_seconds()} s"
            )

            step_metrics_ftm = self.calculate_rolling_metrics(
                ftm_acts_denorm, ftm_preds_denorm, self.ftm_target_columns
            )
            step_metrics_stm = self.calculate_rolling_metrics(
                stm_acts_denorm, stm_preds_denorm, self.stm_target_columns
            )

            return {
                "ftm": {
                    "predictions": np.squeeze(np.array(ftm_preds_denorm)),
                    "actual": np.squeeze(np.array(ftm_acts_denorm)),
                    "metrics": step_metrics_ftm,
                },
                "stm": {
                    "predictions": np.squeeze(np.array(stm_preds_denorm)),
                    "actual": np.squeeze(np.array(stm_acts_denorm)),
                    "metrics": step_metrics_stm,
                },
            }

        except Exception as e:
            _logger.error(f"滚动预测失败: {e}")
            raise

    def prediction(
        self, df: pd.DataFrame, model, input_cols, target_cols
    ) -> tuple[np.ndarray, np.ndarray]:
        try:
            X, y_true = self.create_sequences(df, input_cols, target_cols)
            X_tensor = torch.FloatTensor(X)
            with torch.no_grad():
                y_pred = model(X_tensor).numpy()
            return y_pred, y_true
        except Exception as e:
            _logger.error(f"预测失败: {e}")
            raise

    def denormalize_predictions(
        self,
        predictions: np.ndarray,
        target_cols: list[str],
        target_meta: Optional[dict[str, Any]],
    ):
        try:
            if target_meta is None:
                _logger.warning(
                    f"没有找到目标列的归一化参数，返回原始预测结果: {target_cols}"
                )
                return predictions

            # 创建临时DataFrame进行反归一化
            pred_df = pd.DataFrame(predictions, columns=target_cols)

            denorm_df = denormalize(pred_df, target_meta)
            return denorm_df.values
        except Exception as e:
            _logger.error(f"反归一化失败: {e}")
            return predictions

    def calculate_rolling_metrics(
        self,
        y_trues: list[np.ndarray],
        y_preds: list[np.ndarray],
        target_cols: list[str],
    ):
        rolling_metrics = []

        if len(y_trues) != len(y_preds):
            raise ValueError("预测值与真实值长度不同")

        for y_true, y_pred in zip(y_trues, y_preds):
            metrics = self.calculate_metrics(y_true, y_pred, target_cols)
            rolling_metrics.append(metrics)

        return rolling_metrics

    def calculate_metrics(
        self, y_true: np.ndarray, y_pred: np.ndarray, target_cols: list[str]
    ):
        try:
            metrics = {}

            # 整体指标
            mse = mean_squared_error(y_true, y_pred)
            mae = mean_absolute_error(y_true, y_pred)
            rmse = np.sqrt(mse)
            r2 = r2_score(y_true, y_pred) if len(y_true) >= 2 else np.nan

            # 计算MAPE (平均绝对百分比误差)
            mape = np.mean(np.abs((y_true - y_pred) / (y_true + 1e-8))) * 100

            # 计算SMAPE (对称平均绝对百分比误差)
            smape = (
                np.mean(
                    2
                    * np.abs(y_true - y_pred)
                    / (np.abs(y_true) + np.abs(y_pred) + 1e-8)
                )
                * 100
            )

            metrics["overall"] = {
                "MSE": float(mse),
                "MAE": float(mae),
                "RMSE": float(rmse),
                "R2": float(r2),
                "MAPE": float(mape),
                "SMAPE": float(smape),
            }

            # 每个目标列的指标
            metrics["per_column"] = {}
            for i, col in enumerate(target_cols):
                col_true = y_true[:, i]
                col_pred = y_pred[:, i]

                col_mse = mean_squared_error(col_true, col_pred)
                col_mae = mean_absolute_error(col_true, col_pred)
                col_rmse = np.sqrt(col_mse)
                col_r2 = r2_score(col_true, col_pred) if len(col_true) >= 2 else np.nan

                # 计算每列的MAPE和SMAPE
                col_mape = (
                    np.mean(np.abs((col_true - col_pred) / (col_true + 1e-8))) * 100
                )
                col_smape = (
                    np.mean(
                        2
                        * np.abs(col_true - col_pred)
                        / (np.abs(col_true) + np.abs(col_pred) + 1e-8)
                    )
                    * 100
                )

                metrics["per_column"][col] = {
                    "MSE": float(col_mse),
                    "MAE": float(col_mae),
                    "RMSE": float(col_rmse),
                    "R2": float(col_r2),
                    "MAPE": float(col_mape),
                    "SMAPE": float(col_smape),
                }
            return metrics
        except Exception as e:
            _logger.error(f"计算指标失败: {e}")
            return {}


def main():
    try:
        Configs.initialize()
        Logger.initialize()

        predictor = MultiModelPredictor(MODEL_DIR, DATA_FILE_NAME)
        predictor.load_model()  # 加载模型
        dfs = predictor.load_data(predictor.data_file_name)  # 加载数据

        # 单步预测评估
        # standard_prediction(predictor, dfs)

        # 滚动预测评估
        rolling_prediction(predictor, dfs)

    except Exception as e:
        _logger.error(f"预测过程发生错误: {e}")


def standard_prediction(predictor: MultiModelPredictor, dfs: list[pd.DataFrame]):

    for i, df in enumerate(dfs):
        _logger.info(f"处理测试文件 {i+1}/{len(dfs)}")
        results = predictor.standard_pred(df)

        log_metrics(results["ftm"], "FTM")
        log_metrics(results["stm"], "STM")

        plot_standard_prediction(
            results["ftm"]["actual"],
            results["ftm"]["predictions"],
            predictor.ftm_target_columns,
            file_index=i,
            save_path=str(predictor.visual_dir / "standard" / "ftm"),
        )
        plot_standard_prediction(
            results["stm"]["actual"],
            results["stm"]["predictions"],
            predictor.stm_target_columns,
            file_index=i,
            save_path=str(predictor.visual_dir / "standard" / "stm"),
        )


def rolling_prediction(predictor: MultiModelPredictor, dfs: list[pd.DataFrame]):
    """
    处理大量测试数据的滚动预测
    从测试数据集中随机采样序列进行滚动预测
    """
    try:
        _logger.info("开始批量滚动预测...")

        if not dfs:
            _logger.error("没有找到测试数据文件")
            return

        for i, df in enumerate(dfs):
            _logger.info(f"处理测试文件 {i+1}/{len(dfs)}")
            _logger.info(f"序列长度: {len(df)}")

            if len(df) < predictor.seq_length + predictor.rolling_steps:
                _logger.info(f"序列长度不足，跳过")
                continue

            rolling_pred_results = []
            effic_len = (
                len(df) - predictor.seq_length - predictor.rolling_steps
            )  # 序列有效长度
            sample_cnt = min(effic_len, 100)  # 采样个数
            start_idxs = random.sample(range(effic_len), sample_cnt)  # 随意生成样本起点

            for input_start_idx in start_idxs:
                try:
                    rolling_start_idx = input_start_idx + predictor.seq_length
                    rolling_end_idx = rolling_start_idx + predictor.rolling_steps

                    init_df = df.iloc[input_start_idx:rolling_start_idx].copy()
                    next_df = df.iloc[rolling_start_idx:rolling_end_idx].copy()

                    # 进行滚动预测
                    results = predictor.rolling_pred(init_df, next_df)
                    rolling_pred_results.append(results)
                except Exception as e:
                    _logger.error(f"滚动预测失败: {e}")

            for index in range(len(rolling_pred_results)):
                result = rolling_pred_results[index]
                log_rolling_metrics(result)
                plot_standard_prediction(
                    result["ftm"]["actual"],
                    result["ftm"]["predictions"],
                    predictor.ftm_target_columns,
                    file_index=i,
                    save_path=str(predictor.visual_dir / "rolling" / "ftm"),
                    file_name=f"rolling_ftm_{index}",
                )
                plot_standard_prediction(
                    result["stm"]["actual"],
                    result["stm"]["predictions"],
                    predictor.stm_target_columns,
                    file_index=i,
                    save_path=str(predictor.visual_dir / "rolling" / "stm"),
                    file_name=f"rolling_stm_{index}",
                )

    except Exception as e:
        _logger.error(f"批量滚动预测失败: {e}")


def log_rolling_metrics(result: dict):
    try:
        for key, value in result.items():
            _logger.info(f"模型{key}滚动预测完成:")
            for i in range(len(value["metrics"])):
                _logger.info(f"第{i+1}步:")
                _logger.info(f"  整体MAE: {value['metrics'][i]['overall']['MAE']:.4f}")
                _logger.info(f"  整体MSE: {value['metrics'][i]['overall']['MSE']:.4f}")
                _logger.info(
                    f"  整体RMSE: {value['metrics'][i]['overall']['RMSE']:.4f}"
                )
                _logger.info(f"  整体R2: {value['metrics'][i]['overall']['R2']:.4f}")
                _logger.info(
                    f"  整体MAPE: {value['metrics'][i]['overall']['MAPE']:.4f}"
                )
                _logger.info(
                    f"  整体SMAPE: {value['metrics'][i]['overall']['SMAPE']:.4f}"
                )
                for col, metrics in value["metrics"][i]["per_column"].items():
                    _logger.info(
                        f"  {col} - MAE: {metrics['MAE']:.4f}, MSE: {metrics['MSE']:.4f}, RMSE: {metrics['RMSE']:.4f},  R2: {metrics['R2']:.4f}, MAPE: {metrics['MAPE']:.4f}, SMAPE: {metrics['SMAPE']:.4f},"
                    )
    except Exception as e:
        _logger.error(f"打印指标失败: {e}")


def log_metrics(result: dict, model_name: str):
    try:
        if result:
            _logger.info(f"{model_name} 预测完成:")
            _logger.info(f"  整体MAE: {result['metrics']['overall']['MAE']:.4f}")
            _logger.info(f"  整体MSE: {result['metrics']['overall']['MSE']:.4f}")
            _logger.info(f"  整体RMSE: {result['metrics']['overall']['RMSE']:.4f}")
            _logger.info(f"  整体R2: {result['metrics']['overall']['R2']:.4f}")
            _logger.info(f"  整体MAPE: {result['metrics']['overall']['MAPE']:.4f}")
            _logger.info(f"  整体SMAPE: {result['metrics']['overall']['SMAPE']:.4f}")

            # 记录每列的详细指标
            for col, metrics in result["metrics"]["per_column"].items():
                _logger.info(
                    f"  {col} - MAE: {metrics['MAE']:.4f}, MSE: {metrics['MSE']:.4f}, RMSE: {metrics['RMSE']:.4f},  R2: {metrics['R2']:.4f}, MAPE: {metrics['MAPE']:.4f}, SMAPE: {metrics['SMAPE']:.4f},"
                )
    except Exception as e:
        _logger.error(f"打印指标失败: {e}")


def setup_plot_style():
    """设置绘图样式，支持中文显示"""
    plt.style.use("default")
    # 设置中文字体
    plt.rcParams["font.sans-serif"] = ["SimHei", "Microsoft YaHei", "DejaVu Sans"]
    plt.rcParams["axes.unicode_minus"] = False
    plt.rcParams["figure.dpi"] = 300
    plt.rcParams["font.size"] = 10
    plt.rcParams["axes.titlesize"] = 12
    plt.rcParams["axes.labelsize"] = 11
    plt.rcParams["legend.fontsize"] = 9
    plt.rcParams["xtick.labelsize"] = 9
    plt.rcParams["ytick.labelsize"] = 9


def determine_confidence_interval(target_columns: List[str]) -> dict:
    """根据列名确定置信区间"""
    confidence_intervals = {}
    for col in target_columns:
        col_lower = col.lower()
        if "strip" in col_lower:
            confidence_intervals[col] = 5  # stm列置信区间为±5
        else:
            confidence_intervals[col] = 10  # ftm列置信区间为±10
    return confidence_intervals


def plot_standard_prediction(
    actual: np.ndarray,
    predictions: np.ndarray,
    target_columns: List[str],
    file_index: int = 0,
    save_path: Optional[str] = None,
    file_name: Optional[str] = None,
    max_points_per_page: int = 256,
) -> None:
    """
    绘制标准预测结果对比图表，支持数据分页显示

    Args:
        actual: 实际值数组，形状为 (n_samples, n_targets)
        predictions: 预测值数组，形状为 (n_samples, n_targets)
        target_columns: 目标列名列表
        file_index: 文件索引，用于标题显示
        save_path: 保存路径，如果为None则不保存
        max_points_per_page: 每页最大数据点数，默认512
    """
    try:
        setup_plot_style()

        if actual.shape[0] == 0:
            _logger.warning("没有数据可供绘制")
            return

        # 确定置信区间
        confidence_intervals = determine_confidence_interval(target_columns)

        # 计算需要的页数
        n_samples = actual.shape[0]
        n_pages = (n_samples + max_points_per_page - 1) // max_points_per_page

        _logger.info(
            f"数据总量: {n_samples}, 分页数: {n_pages}, 每页最大点数: {max_points_per_page}"
        )

        for page in range(n_pages):
            start_idx = page * max_points_per_page
            end_idx = min((page + 1) * max_points_per_page, n_samples)

            # 提取当前页的数据
            page_actual = actual[start_idx:end_idx]
            page_predictions = predictions[start_idx:end_idx]
            page_x_axis = np.arange(start_idx, end_idx)

            _plot_page_data(
                actual=page_actual,
                predictions=page_predictions,
                target_columns=target_columns,
                confidence_intervals=confidence_intervals,
                x_axis=page_x_axis,
                file_index=file_index,
                page_index=page,
                total_pages=n_pages,
                save_path=save_path,
                file_name=file_name,
            )

    except Exception as e:
        _logger.error(f"绘制标准预测图失败: {e}")
        raise


def _plot_page_data(
    actual: np.ndarray,
    predictions: np.ndarray,
    target_columns: List[str],
    confidence_intervals: dict,
    x_axis: np.ndarray,
    file_index: int,
    page_index: int,
    total_pages: int,
    save_path: Optional[str] = None,
    file_name: Optional[str] = None,
) -> None:
    """
    绘制单页数据的预测对比图表

    Args:
        actual: 当前页实际值数组
        predictions: 当前页预测值数组
        target_columns: 目标列名列表
        confidence_intervals: 置信区间配置字典
        x_axis: x轴数据
        file_index: 文件索引
        page_index: 页面索引
        total_pages: 总页数
        save_path: 保存路径
    """
    if file_name is None:
        file_name = "prediction"

    try:
        n_columns = len(target_columns)

        # 创建纵向排列的子图
        fig, axes = plt.subplots(n_columns, 1, figsize=(16, 4 * n_columns), sharex=True)

        # 如果只有一列，确保axes是数组
        if n_columns == 1:
            axes = [axes]

        # 定义颜色
        colors = ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728"]

        for i, col in enumerate(target_columns):
            ax = axes[i]

            # 绘制置信区间（如果有）
            confidence_interval = confidence_intervals.get(col)
            if confidence_interval is not None:
                actual_col = actual[:, i]
                upper_bound = actual_col + confidence_interval
                lower_bound = actual_col - confidence_interval

                ax.fill_between(
                    x_axis,
                    lower_bound,
                    upper_bound,
                    color=colors[2],
                    alpha=0.3,
                    label=f"置信区间 (±{confidence_interval}°C)",
                    zorder=1,
                )

            # 绘制实际值
            ax.plot(
                x_axis,
                actual[:, i],
                label="实际值",
                color=colors[0],
                linewidth=2.0,
                alpha=0.9,
                zorder=3,
            )

            # 绘制预测值
            ax.plot(
                x_axis,
                predictions[:, i],
                label="预测值",
                color=colors[1],
                linewidth=2.0,
                linestyle="--",
                alpha=0.9,
                zorder=2,
            )

            # 设置子图标题和标签
            ax.set_title(f"{col} - 预测对比", fontsize=12, fontweight="bold")
            ax.set_ylabel("温度 (°C)", fontsize=11)
            ax.legend(loc="upper right", fontsize=9)
            ax.grid(True, alpha=0.3)

            # 设置y轴范围，确保数据可见
            y_min = min(actual[:, i].min(), predictions[:, i].min())
            y_max = max(actual[:, i].max(), predictions[:, i].max())
            y_range = y_max - y_min
            ax.set_ylim(y_min - 0.1 * y_range, y_max + 0.1 * y_range)

        # 设置x轴标签（只在最后一个子图）
        axes[-1].set_xlabel("数据点索引", fontsize=11)

        # 设置总标题
        page_info = f"第{page_index + 1}页/共{total_pages}页" if total_pages > 1 else ""
        fig.suptitle(
            f"文件 {file_index + 1} - 标准预测结果对比 {page_info}",
            fontsize=16,
            fontweight="bold",
            y=0.98,
        )

        # 调整布局
        plt.tight_layout()
        plt.subplots_adjust(top=0.93, hspace=0.4)

        # 保存图表
        if save_path:
            save_dir = Path(save_path)
            save_dir.mkdir(parents=True, exist_ok=True)

            if total_pages > 1:
                save_file = (
                    save_dir
                    / f"{file_name}_file_{file_index + 1}_page_{page_index + 1}.png"
                )
            else:
                save_file = save_dir / f"{file_name}_file_{file_index + 1}.png"

            plt.savefig(save_file, dpi=300, bbox_inches="tight", facecolor="white")
            _logger.info(f"标准预测图已保存到: {save_file}")

        # 关闭图表释放内存
        plt.close(fig)

    except Exception as e:
        _logger.error(f"绘制页面数据失败: {e}")
        raise


if __name__ == "__main__":
    main()
