from datetime import datetime
from config import Configs, Logger
from trainers import train_seq2seq_model
from predictors.seq2seq_predictor import Seq2SeqEvaluator

if __name__ == "__main__":
    Configs.initialize()
    Logger.initialize()

    start_time = datetime.strptime("2025-05-01", "%Y-%m-%d")
    end_time = datetime.strptime("2025-06-30", "%Y-%m-%d")
    work_dir = f"data/test-seq2seq/{datetime.now().strftime('%Y%m%d')}"
    # work_dir = f"data/test-seq2seq/20250730"

    train_seq2seq_model(start_time, end_time)
    evaluator = Seq2SeqEvaluator(work_dir, "data_test_*", samples_num=1000)
    evaluator.evaluate()
