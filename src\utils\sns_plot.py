import logging
from pathlib import Path
import numpy as np
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional, Literal
from matplotlib.figure import Figure
from matplotlib.axes import Axes


class SeabornVisualizer:
    """
    强大的Seaborn可视化工具类
    支持多种图表类型和深度自定义

    功能特点：
    - 内置专业美观的默认样式
    - 支持折线图、柱状图、箱线图、小提琴图、热力图等
    - 自动颜色主题管理
    - 智能标注和统计信息添加
    - 多子图布局支持
    - 图表元素精细控制

    使用示例：
    >>> viz = SeabornVisualizer()
    >>> viz.lineplot(data=df, x='date', y='sales', hue='region')
    >>> viz.show()
    """

    def __init__(
        self,
        style: Literal["darkgrid", "whitegrid", "dark", "white", "ticks"] = "whitegrid",
        palette: str = "muted",
        context: Literal["paper", "notebook", "talk", "poster"] = "notebook",
        font_scale: float = 1.1,
    ):
        """
        初始化可视化工具

        :param style: Seaborn样式 (darkgrid, whitegrid, dark, white, ticks)
        :param palette: 颜色主题 (deep, muted, bright, pastel, dark, colorblind)
        :param context: 绘图上下文 (paper, notebook, talk, poster)
        :param font_scale: 字体缩放比例
        """
        self.set_style(style, palette, context, font_scale)
        self.fig: Optional[Figure] = None
        self.ax: Optional[Axes] = None
        self.logger = logging.getLogger(self.__class__.__name__)

    def set_style(
        self,
        style: Literal["darkgrid", "whitegrid", "dark", "white", "ticks"],
        palette: str,
        context: Literal["paper", "notebook", "talk", "poster"],
        font_scale: float,
    ):
        """设置绘图样式"""
        sns.set_theme(
            style=style, palette=palette, context=context, font_scale=font_scale
        )
        self._set_custom_rc_params()

    def _set_custom_rc_params(self):
        """设置增强的RC参数"""
        plt.rcParams.update(
            {
                "figure.dpi": 120,
                "savefig.dpi": 300,
                "axes.titleweight": "bold",
                "axes.titlelocation": "left",
                "axes.titlesize": 14,
                "axes.titlepad": 12,
                "axes.spines.top": False,
                "axes.spines.right": False,
                "grid.alpha": 0.15,
                "legend.frameon": True,
                "legend.edgecolor": "none",
                "legend.fancybox": True,
                "legend.fontsize": 11,
                "font.sans-serif": ["SimHei", "DejaVu Sans"],
            }
        )

    def create_figure(self, figsize: Tuple[float, float] = (10, 6)) -> Axes:
        """创建新的图形"""
        self.fig, self.ax = plt.subplots(figsize=figsize)
        return self.ax

    def apply_color_palette(self, palette: str, n_colors: int = 6):
        """应用新的颜色主题"""
        sns.set_palette(palette, n_colors)

    def _process_plot_params(self, **kwargs):
        """处理通用绘图参数"""
        if self.ax is None:
            return

        title = kwargs.pop("title", None)
        xlabel = kwargs.pop("xlabel", None)
        ylabel = kwargs.pop("ylabel", None)
        legend = kwargs.pop("legend", True)

        if title:
            self.ax.set_title(title, fontsize=14, pad=12)
        if xlabel:
            self.ax.set_xlabel(xlabel, fontsize=12)
        if ylabel:
            self.ax.set_ylabel(ylabel, fontsize=12)
        if legend and self.ax.get_legend() is not None:
            self.ax.legend(loc="best", framealpha=0.8)

        # 自动旋转x轴标签
        if self.ax.get_xticklabels():
            rotation = kwargs.pop(
                "x_rotation", 45 if len(self.ax.get_xticklabels()) > 5 else 0
            )
            plt.setp(
                self.ax.get_xticklabels(),
                rotation=rotation,
                ha="right" if rotation else "center",
            )

        # 添加网格线
        if kwargs.pop("grid", True):
            self.ax.grid(alpha=0.15, linestyle="--")

    def show(self):
        """显示图表"""
        if self.fig:
            plt.tight_layout()
            plt.show()

    def save(
        self,
        filename: Path,
        dpi: int = 300,
        transparent: bool = False,
        bbox_inches: str = "tight",
    ):
        """
        保存图表到文件

        :param filename: 文件名 (带扩展名)
        :param dpi: 分辨率
        :param transparent: 是否透明背景
        :param bbox_inches: 边界框设置
        """
        if self.fig:
            self.fig.savefig(
                filename, dpi=dpi, transparent=transparent, bbox_inches=bbox_inches
            )
            self.logger.debug(f"图表已保存至: {filename}")

    def close(self):
        """关闭图表"""
        if self.fig:
            plt.close(self.fig)
            self.fig = None
            self.ax = None

    def plot_line_chart(
        self,
        y_true: pd.DataFrame,
        y_pred: pd.DataFrame,
        columns: List[str],
        ci: float = 5.0,
        figsize: Optional[Tuple[float, float]] = None,
        **kwargs,
    ) -> Figure:
        """
        绘制真实值与预测值的折线对比图，支持多列数据和置信区间

        :param y_true: 真实值数据框
        :param y_pred: 预测值数据框
        :param columns: 需要绘制的数据列名列表
        :param ci: 置信区间参数（如5表示±5的置信区间）
        :param figsize: 图形尺寸，如果为None则自动计算
        :param kwargs: 其他绘图参数
        :return: matplotlib图形对象
        """
        # 验证输入数据
        if not isinstance(y_true, pd.DataFrame) or not isinstance(y_pred, pd.DataFrame):
            raise ValueError("y_true 和 y_pred 必须是 pandas.DataFrame 类型")

        if not columns:
            raise ValueError("columns 列表不能为空")

        # 检查列是否存在
        missing_cols_true = [col for col in columns if col not in y_true.columns]
        missing_cols_pred = [col for col in columns if col not in y_pred.columns]

        if missing_cols_true:
            raise ValueError(f"y_true 中缺少列: {missing_cols_true}")
        if missing_cols_pred:
            raise ValueError(f"y_pred 中缺少列: {missing_cols_pred}")

        # 计算图形尺寸
        n_cols = len(columns)
        if figsize is None:
            fig_width = 12
            fig_height = max(4 * n_cols, 8)
            figsize = (fig_width, fig_height)

        # 创建子图
        self.fig, axes = plt.subplots(n_cols, 1, figsize=figsize, squeeze=False)
        axes = axes.flatten()

        # 为每个列绘制子图
        for i, col in enumerate(columns):
            ax = axes[i]

            # 获取数据
            true_data = y_true[col].dropna()
            pred_data = y_pred[col].dropna()

            # 确保数据长度一致
            min_len = min(len(true_data), len(pred_data))
            if min_len == 0:
                ax.text(
                    0.5,
                    0.5,
                    f'列 "{col}" 无有效数据',
                    ha="center",
                    va="center",
                    transform=ax.transAxes,
                )
                ax.set_title(col, fontsize=14, pad=12)
                continue

            true_data = true_data.iloc[:min_len]
            pred_data = pred_data.iloc[:min_len]

            # 创建x轴索引
            x_index = np.arange(len(true_data))

            # 绘制真实值折线
            ax.plot(
                x_index,
                true_data,
                label="真实值",
                linewidth=2,
                alpha=0.8,
                color="#2E86AB",
            )

            # 绘制预测值折线
            ax.plot(
                x_index,
                pred_data,
                label="预测值",
                linewidth=2,
                alpha=0.8,
                color="#F24236",
            )

            # 绘制置信区间
            if ci > 0:
                upper_bound = true_data + ci
                lower_bound = true_data - ci
                ax.fill_between(
                    x_index,
                    lower_bound,
                    upper_bound,
                    alpha=0.2,
                    color="#2E86AB",
                    label=f"置信区间 (±{ci})",
                )

            # 设置子图标题和标签
            ax.set_title(col, fontsize=14, pad=12, fontweight="bold")
            ax.set_xlabel("样本索引", fontsize=12)
            ax.set_ylabel("数值", fontsize=12)

            # 添加图例
            ax.legend(loc="best", framealpha=0.8, fontsize=10)

            # 添加网格
            ax.grid(alpha=0.3, linestyle="--")

            # 设置坐标轴样式
            ax.spines["top"].set_visible(False)
            ax.spines["right"].set_visible(False)

            # 计算并显示统计信息
            mae = np.mean(np.abs(true_data - pred_data))
            rmse = np.sqrt(np.mean((true_data - pred_data) ** 2))

            # 在图上添加统计信息
            stats_text = f"MAE: {mae:.3f}\nRMSE: {rmse:.3f}"
            ax.text(
                0.02,
                0.98,
                stats_text,
                transform=ax.transAxes,
                verticalalignment="top",
                fontsize=9,
                bbox=dict(boxstyle="round", facecolor="white", alpha=0.8),
            )

        # 调整布局
        plt.tight_layout(pad=3.0)

        # 设置整体标题
        if "suptitle" in kwargs:
            self.fig.suptitle(kwargs["suptitle"], fontsize=16, y=0.98)
            plt.subplots_adjust(top=0.95)

        return self.fig
