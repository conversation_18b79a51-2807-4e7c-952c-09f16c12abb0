#!/usr/bin/env python3
"""
Seq2Seq Lightning 模型使用示例

本示例展示如何使用 LitSeq2Seq 和 LitSeq2SeqWithAttention 模型进行训练和预测。
"""

import torch
import torch.nn as nn
import lightning as L
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
import numpy as np

# 导入模型
from src.model import LitSeq2Seq, LitSeq2SeqWithAttention


def generate_synthetic_data(num_samples=1000, past_seq_len=20, future_seq_len=10):
    """
    生成合成的时间序列数据用于演示
    
    Args:
        num_samples: 样本数量
        past_seq_len: 过去序列长度
        future_seq_len: 未来序列长度
    
    Returns:
        x_past, x_future, y: 训练数据
    """
    print("生成合成数据...")
    
    # 设置随机种子以确保可重复性
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 生成基础时间序列
    time_steps = past_seq_len + future_seq_len
    
    x_past_list = []
    x_future_list = []
    y_list = []
    
    for i in range(num_samples):
        # 生成带有趋势和噪声的时间序列
        t = np.linspace(0, 4*np.pi, time_steps)
        
        # 基础信号：正弦波 + 趋势 + 噪声
        base_signal = np.sin(t) + 0.1 * t + 0.1 * np.random.randn(time_steps)
        
        # 过去的特征（多个特征）
        past_features = np.column_stack([
            base_signal[:past_seq_len],  # 主要信号
            np.cos(t[:past_seq_len]),    # 余弦特征
            np.gradient(base_signal[:past_seq_len]),  # 梯度特征
            np.cumsum(base_signal[:past_seq_len]) / np.arange(1, past_seq_len + 1),  # 累积平均
            np.random.randn(past_seq_len) * 0.05,  # 噪声特征
        ])
        
        # 未来的已知特征（较少特征）
        future_features = np.column_stack([
            np.cos(t[past_seq_len:]),  # 已知的周期性特征
            np.ones(future_seq_len) * (i % 3),  # 类别特征
            np.linspace(0, 1, future_seq_len),  # 时间特征
        ])
        
        # 目标变量（我们要预测的）
        target = base_signal[past_seq_len:].reshape(-1, 1)
        
        x_past_list.append(past_features)
        x_future_list.append(future_features)
        y_list.append(target)
    
    # 转换为张量
    x_past = torch.FloatTensor(np.array(x_past_list))
    x_future = torch.FloatTensor(np.array(x_future_list))
    y = torch.FloatTensor(np.array(y_list))
    
    print(f"数据形状:")
    print(f"  x_past: {x_past.shape}")
    print(f"  x_future: {x_future.shape}")
    print(f"  y: {y.shape}")
    
    return x_past, x_future, y


def train_model(model, train_loader, val_loader, max_epochs=20):
    """
    训练模型
    
    Args:
        model: Lightning 模型
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        max_epochs: 最大训练轮数
    
    Returns:
        训练好的模型
    """
    print(f"\n开始训练 {model.__class__.__name__}...")
    
    # 创建训练器
    trainer = L.Trainer(
        max_epochs=max_epochs,
        accelerator="auto",
        devices="auto",
        precision="16-mixed" if torch.cuda.is_available() else "32-true",
        log_every_n_steps=10,
        enable_progress_bar=True,
        enable_model_summary=True,
    )
    
    # 训练模型
    trainer.fit(model, train_loader, val_loader)
    
    return model


def evaluate_model(model, test_loader):
    """
    评估模型性能
    
    Args:
        model: 训练好的模型
        test_loader: 测试数据加载器
    
    Returns:
        评估结果
    """
    print(f"\n评估 {model.__class__.__name__}...")
    
    trainer = L.Trainer(
        accelerator="auto",
        devices="auto",
        logger=False,
    )
    
    # 测试模型
    test_results = trainer.test(model, test_loader)
    
    return test_results[0]


def visualize_predictions(model, x_past, x_future, y, num_samples=3):
    """
    可视化预测结果
    
    Args:
        model: 训练好的模型
        x_past, x_future, y: 测试数据
        num_samples: 要可视化的样本数量
    """
    print(f"\n可视化 {model.__class__.__name__} 的预测结果...")
    
    model.eval()
    with torch.no_grad():
        predictions = model(x_past[:num_samples], x_future[:num_samples])
    
    fig, axes = plt.subplots(num_samples, 1, figsize=(12, 4*num_samples))
    if num_samples == 1:
        axes = [axes]
    
    for i in range(num_samples):
        ax = axes[i]
        
        # 绘制过去的数据（只显示主要特征）
        past_len = x_past.shape[1]
        past_data = x_past[i, :, 0].numpy()  # 主要特征
        ax.plot(range(past_len), past_data, 'b-', label='过去数据', linewidth=2)
        
        # 绘制真实的未来数据
        future_len = y.shape[1]
        true_future = y[i, :, 0].numpy()
        future_x = range(past_len, past_len + future_len)
        ax.plot(future_x, true_future, 'g-', label='真实未来', linewidth=2)
        
        # 绘制预测的未来数据
        pred_future = predictions[i, :, 0].numpy()
        ax.plot(future_x, pred_future, 'r--', label='预测未来', linewidth=2)
        
        ax.axvline(x=past_len-0.5, color='k', linestyle=':', alpha=0.7, label='预测起点')
        ax.set_title(f'样本 {i+1} - {model.__class__.__name__}')
        ax.set_xlabel('时间步')
        ax.set_ylabel('值')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()


def visualize_attention_weights(attention_model, x_past, x_future, sample_idx=0):
    """
    可视化注意力权重
    
    Args:
        attention_model: 带注意力的模型
        x_past, x_future: 输入数据
        sample_idx: 要可视化的样本索引
    """
    print(f"\n可视化注意力权重...")
    
    # 获取注意力权重
    attention_weights = attention_model.get_attention_weights(
        x_past[sample_idx:sample_idx+1], 
        x_future[sample_idx:sample_idx+1]
    )
    
    # 转换为numpy数组
    attention_matrix = torch.stack([w[0] for w in attention_weights]).numpy()
    
    # 创建热力图
    plt.figure(figsize=(12, 8))
    plt.imshow(attention_matrix, cmap='Blues', aspect='auto')
    plt.xlabel('过去时间步')
    plt.ylabel('未来时间步')
    plt.title(f'样本 {sample_idx} 的注意力权重热力图')
    plt.colorbar(label='注意力权重')
    
    # 添加网格
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()


def main():
    """主函数"""
    print("Seq2Seq Lightning 模型使用示例")
    print("=" * 50)
    
    # 生成数据
    x_past, x_future, y = generate_synthetic_data(
        num_samples=800, 
        past_seq_len=20, 
        future_seq_len=10
    )
    
    # 分割数据
    train_size = int(0.7 * len(x_past))
    val_size = int(0.15 * len(x_past))
    
    train_data = TensorDataset(x_past[:train_size], x_future[:train_size], y[:train_size])
    val_data = TensorDataset(x_past[train_size:train_size+val_size], 
                           x_future[train_size:train_size+val_size], 
                           y[train_size:train_size+val_size])
    test_data = TensorDataset(x_past[train_size+val_size:], 
                            x_future[train_size+val_size:], 
                            y[train_size+val_size:])
    
    # 创建数据加载器
    batch_size = 32
    train_loader = DataLoader(train_data, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_data, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_data, batch_size=batch_size, shuffle=False)
    
    print(f"\n数据分割:")
    print(f"  训练集: {len(train_data)} 样本")
    print(f"  验证集: {len(val_data)} 样本")
    print(f"  测试集: {len(test_data)} 样本")
    
    # 模型参数
    num_past_features = x_past.shape[2]
    num_future_features = x_future.shape[2]
    num_target_features = y.shape[2]
    
    # 创建基础 Seq2Seq 模型
    basic_model = LitSeq2Seq(
        num_past_features=num_past_features,
        num_future_features=num_future_features,
        num_target_features=num_target_features,
        hidden_dim=64,
        num_layers=2,
        dropout_rate=0.1,
        learning_rate=1e-3,
        loss_type="mse",
        scheduler_type="plateau"
    )
    
    # 创建带注意力的 Seq2Seq 模型
    attention_model = LitSeq2SeqWithAttention(
        num_past_features=num_past_features,
        num_future_features=num_future_features,
        num_target_features=num_target_features,
        hidden_dim=64,
        num_layers=2,
        dropout_rate=0.1,
        learning_rate=1e-3,
        loss_type="mse",
        scheduler_type="plateau"
    )
    
    # 训练模型
    max_epochs = 10  # 为了演示，使用较少的轮数
    
    trained_basic = train_model(basic_model, train_loader, val_loader, max_epochs)
    trained_attention = train_model(attention_model, train_loader, val_loader, max_epochs)
    
    # 评估模型
    basic_results = evaluate_model(trained_basic, test_loader)
    attention_results = evaluate_model(trained_attention, test_loader)
    
    print(f"\n模型性能比较:")
    print(f"基础 Seq2Seq:")
    print(f"  测试损失: {basic_results['test_loss']:.4f}")
    print(f"  测试MAE: {basic_results['test_mae']:.4f}")
    print(f"  测试RMSE: {basic_results['test_rmse']:.4f}")
    
    print(f"\n带注意力的 Seq2Seq:")
    print(f"  测试损失: {attention_results['test_loss']:.4f}")
    print(f"  测试MAE: {attention_results['test_mae']:.4f}")
    print(f"  测试RMSE: {attention_results['test_rmse']:.4f}")
    
    # 可视化预测结果
    test_x_past = x_past[train_size+val_size:]
    test_x_future = x_future[train_size+val_size:]
    test_y = y[train_size+val_size:]
    
    visualize_predictions(trained_basic, test_x_past, test_x_future, test_y, num_samples=2)
    visualize_predictions(trained_attention, test_x_past, test_x_future, test_y, num_samples=2)
    
    # 可视化注意力权重
    visualize_attention_weights(trained_attention, test_x_past, test_x_future, sample_idx=0)
    
    print("\n示例完成！")


if __name__ == "__main__":
    main()
