import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class PositionalEncoding(nn.Module):
    """位置编码模块，为序列添加位置信息"""

    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(
            torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model)
        )
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        self.register_buffer("pe", pe)

    def forward(self, x):
        return x + self.pe[: x.size(0), :]


class MultiHeadAttention(nn.Module):
    """多头注意力机制"""

    def __init__(self, d_model, num_heads, dropout=0.1):
        super(MultiHeadAttention, self).__init__()
        assert d_model % num_heads == 0

        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads

        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)

        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)

    def forward(self, query, key, value, mask=None):
        batch_size = query.size(0)

        # 保存残差连接的输入
        residual = query

        # 线性变换并重塑为多头
        Q = (
            self.w_q(query)
            .view(batch_size, -1, self.num_heads, self.d_k)
            .transpose(1, 2)
        )
        K = self.w_k(key).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        V = (
            self.w_v(value)
            .view(batch_size, -1, self.num_heads, self.d_k)
            .transpose(1, 2)
        )

        # 计算注意力
        attention_output, attention_weights = self.scaled_dot_product_attention(
            Q, K, V, mask
        )

        # 重塑并通过输出线性层
        attention_output = (
            attention_output.transpose(1, 2)
            .contiguous()
            .view(batch_size, -1, self.d_model)
        )
        output = self.w_o(attention_output)

        # 残差连接和层归一化
        output = self.layer_norm(output + residual)

        return output, attention_weights

    def scaled_dot_product_attention(self, Q, K, V, mask=None):
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)

        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)

        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)

        output = torch.matmul(attention_weights, V)
        return output, attention_weights


class Encoder(nn.Module):
    def __init__(self, num_past_features, hidden_dim, num_layers, dropout_rate):
        super(Encoder, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers

        # 输入投影层
        self.input_projection = nn.Linear(num_past_features, hidden_dim)

        # LSTM层
        self.lstm = nn.LSTM(
            input_size=hidden_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout_rate if num_layers > 1 else 0,
            bidirectional=False,
        )

        # 多头注意力层
        self.self_attention = MultiHeadAttention(
            hidden_dim, num_heads=8, dropout=dropout_rate
        )

        # 前馈网络
        self.feed_forward = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 4),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim * 4, hidden_dim),
            nn.Dropout(dropout_rate),
        )

        self.layer_norm = nn.LayerNorm(hidden_dim)
        self.dropout = nn.Dropout(dropout_rate)

    def forward(self, x_past):
        # 输入投影
        x = self.input_projection(x_past)

        # LSTM处理
        lstm_outputs, (hidden, cell) = self.lstm(x)

        # 自注意力机制
        attended_outputs, _ = self.self_attention(
            lstm_outputs, lstm_outputs, lstm_outputs
        )

        # 前馈网络
        residual = attended_outputs
        ff_output = self.feed_forward(attended_outputs)
        outputs = self.layer_norm(ff_output + residual)

        # outputs 形状: [batch_size, PAST_SEQ_LEN, hidden_dim]
        # hidden, cell 形状: [num_layers, batch_size, hidden_dim]
        return outputs, hidden, cell


class ImprovedAttention(nn.Module):
    """改进的注意力机制，支持多种注意力类型"""

    def __init__(self, hidden_dim, attention_type="additive", num_heads=1):
        super(ImprovedAttention, self).__init__()
        self.hidden_dim = hidden_dim
        self.attention_type = attention_type
        self.num_heads = num_heads

        if attention_type == "additive":
            # 加性注意力（原始实现）
            self.attn = nn.Linear(hidden_dim * 2, hidden_dim)
            self.v = nn.Parameter(torch.rand(hidden_dim))
        elif attention_type == "multiplicative":
            # 乘性注意力
            self.attn = nn.Linear(hidden_dim, hidden_dim)
        elif attention_type == "multi_head":
            # 多头注意力
            self.multi_head_attn = MultiHeadAttention(hidden_dim, num_heads)

        self.dropout = nn.Dropout(0.1)

    def forward(self, hidden, encoder_outputs):
        """
        :param hidden: 解码器当前隐藏状态 [num_layers, batch_size, hidden_dim]
        :param encoder_outputs: 编码器输出 [batch_size, seq_len, hidden_dim]
        :return: 注意力权重 [batch_size, seq_len]
        """
        if self.attention_type == "additive":
            return self._additive_attention(hidden, encoder_outputs)
        elif self.attention_type == "multiplicative":
            return self._multiplicative_attention(hidden, encoder_outputs)
        elif self.attention_type == "multi_head":
            return self._multi_head_attention(hidden, encoder_outputs)

    def _additive_attention(self, hidden, encoder_outputs):
        batch_size = encoder_outputs.size(0)
        seq_len = encoder_outputs.size(1)

        # 使用最顶层的隐藏状态
        hidden = hidden[-1].unsqueeze(1).repeat(1, seq_len, 1)

        # 拼接并计算注意力分数
        concat_out = torch.cat((hidden, encoder_outputs), dim=2)
        energy = torch.tanh(self.attn(concat_out))
        energy = energy.transpose(1, 2)

        v = self.v.repeat(batch_size, 1).unsqueeze(1)
        attention_scores = torch.bmm(v, energy).squeeze(1)

        return F.softmax(attention_scores, dim=1)

    def _multiplicative_attention(self, hidden, encoder_outputs):
        # 使用最顶层的隐藏状态
        hidden = hidden[-1]  # [batch_size, hidden_dim]

        # 计算注意力分数
        scores = torch.bmm(encoder_outputs, hidden.unsqueeze(2)).squeeze(2)
        return F.softmax(scores, dim=1)

    def _multi_head_attention(self, hidden, encoder_outputs):
        # 使用最顶层的隐藏状态作为query
        query = hidden[-1].unsqueeze(1)  # [batch_size, 1, hidden_dim]

        # 多头注意力
        attended_output, attention_weights = self.multi_head_attn(
            query, encoder_outputs, encoder_outputs
        )

        # 返回平均注意力权重
        return attention_weights.mean(dim=1).squeeze(1)


class DecoderWithAttention(nn.Module):
    """改进的带注意力的解码器"""

    def __init__(
        self,
        num_future_features,
        num_target_features,
        hidden_dim,
        num_layers,
        dropout_rate,
        attention_type="additive",
        use_teacher_forcing=True,
    ):
        super(DecoderWithAttention, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.use_teacher_forcing = use_teacher_forcing

        # 改进的注意力机制
        self.attention = ImprovedAttention(hidden_dim, attention_type)

        # 输入投影层
        self.input_projection = nn.Linear(num_future_features, hidden_dim)

        # LSTM层 - 输入为投影后的特征 + 上下文向量
        self.lstm = nn.LSTM(
            input_size=hidden_dim * 2,  # 投影特征 + 上下文向量
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout_rate if num_layers > 1 else 0,
        )

        # 输出层
        self.output_projection = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim // 2, num_target_features),
        )

        # 层归一化
        self.layer_norm = nn.LayerNorm(hidden_dim)
        self.dropout = nn.Dropout(dropout_rate)

    def forward(self, x_future_step, hidden, cell, encoder_outputs):
        """
        改进的解码器前向传播
        :param x_future_step: 单个时间步的未来输入 [batch_size, 1, num_future_features]
        :param hidden: 解码器隐藏状态 [num_layers, batch_size, hidden_dim]
        :param cell: 解码器细胞状态 [num_layers, batch_size, hidden_dim]
        :param encoder_outputs: 编码器输出 [batch_size, seq_len, hidden_dim]
        :return: 预测值，新的隐藏状态，新的细胞状态
        """
        # 1. 输入投影
        projected_input = self.input_projection(x_future_step)

        # 2. 计算注意力权重和上下文向量
        attention_weights = self.attention(hidden, encoder_outputs)
        context_vector = torch.bmm(attention_weights.unsqueeze(1), encoder_outputs)

        # 3. 拼接投影输入和上下文向量
        lstm_input = torch.cat((projected_input, context_vector), dim=2)

        # 4. LSTM处理
        lstm_output, (hidden, cell) = self.lstm(lstm_input, (hidden, cell))

        # 5. 层归一化和dropout
        lstm_output = self.layer_norm(lstm_output)
        lstm_output = self.dropout(lstm_output)

        # 6. 输出投影
        prediction = self.output_projection(lstm_output.squeeze(1))

        return prediction, hidden, cell


class Seq2SeqWithAttention(nn.Module):
    """改进的Seq2Seq注意力模型"""

    def __init__(self, encoder, decoder, use_teacher_forcing=True):
        super(Seq2SeqWithAttention, self).__init__()
        self.encoder = encoder
        self.decoder = decoder
        self.use_teacher_forcing = use_teacher_forcing

    def forward(self, x_past, x_future, y_future=None, teacher_forcing_ratio=0.5):
        """
        改进的前向传播，支持teacher forcing
        :param x_past: 过去的数据 [batch_size, past_seq_len, num_past_features]
        :param x_future: 未来的已知数据 [batch_size, future_seq_len, num_future_features]
        :param y_future: 未来的目标数据（训练时使用） [batch_size, future_seq_len, num_target_features]
        :param teacher_forcing_ratio: teacher forcing的使用比例
        :return: 预测结果
        """
        batch_size = x_past.size(0)
        future_seq_len = x_future.size(1)
        num_target_features = self.decoder.output_projection[-1].out_features
        device = x_past.device

        # 创建输出张量
        predictions = torch.zeros(batch_size, future_seq_len, num_target_features).to(
            device
        )

        # 1. 编码器处理
        encoder_outputs, hidden, cell = self.encoder(x_past)

        # 2. 解码器逐步处理
        for t in range(future_seq_len):
            # 获取当前时间步的输入
            x_future_step = x_future[:, t, :].unsqueeze(1)

            # 解码一步
            prediction, hidden, cell = self.decoder(
                x_future_step, hidden, cell, encoder_outputs
            )

            # 存储预测结果
            predictions[:, t, :] = prediction

            # Teacher forcing（仅在训练时使用）
            if (
                self.training
                and y_future is not None
                and torch.rand(1).item() < teacher_forcing_ratio
            ):
                # 使用真实标签作为下一步的输入（如果需要的话）
                pass  # 当前实现中不需要，因为x_future已经提供了所有输入

        return predictions

    def generate(self, x_past, x_future, max_length=None):
        """
        生成模式，用于推理
        :param x_past: 过去的数据
        :param x_future: 未来的已知数据
        :param max_length: 最大生成长度
        :return: 生成的序列
        """
        self.eval()
        with torch.no_grad():
            return self.forward(x_past, x_future, teacher_forcing_ratio=0.0)

    def get_attention_weights(self, x_past, x_future):
        """
        获取注意力权重用于可视化
        :param x_past: 过去的数据
        :param x_future: 未来的已知数据
        :return: 注意力权重列表
        """
        self.eval()
        with torch.no_grad():
            batch_size = x_past.size(0)
            future_seq_len = x_future.size(1)
            attention_weights_list = []

            # 编码器处理
            encoder_outputs, hidden, cell = self.encoder(x_past)

            # 逐步解码并收集注意力权重
            for t in range(future_seq_len):
                x_future_step = x_future[:, t, :].unsqueeze(1)

                # 计算注意力权重
                attention_weights = self.decoder.attention(hidden, encoder_outputs)
                attention_weights_list.append(attention_weights)

                # 进行一步解码
                _, hidden, cell = self.decoder(
                    x_future_step, hidden, cell, encoder_outputs
                )

            return attention_weights_list
