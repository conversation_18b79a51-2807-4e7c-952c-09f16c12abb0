import torch
import torch.nn as nn
import torch.nn.functional as F


class Encoder(nn.Module):
    def __init__(self, num_past_features, hidden_dim, num_layers, dropout_rate):
        super(Encoder, self).__init__()
        self.lstm = nn.LSTM(
            input_size=num_past_features,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout_rate if num_layers > 1 else 0,
        )

    def forward(self, x_past):
        # 现在我们需要 `outputs`
        outputs, (hidden, cell) = self.lstm(x_past)
        # outputs 形状: [batch_size, PAST_SEQ_LEN, hidden_dim]
        # hidden, cell 形状: [num_layers, batch_size, hidden_dim]
        return outputs, hidden, cell


class DecoderWithAttention(nn.Module):  # 全新的带注意力的解码器
    def __init__(
        self,
        num_future_features,
        num_target_features,
        hidden_dim,
        num_layers,
        dropout_rate,
    ):
        super(DecoderWithAttention, self).__init__()
        self.attention = Attention(hidden_dim)

        # LSTM的输入维度变了：它现在接收 `未来的特征` + `上一步的上下文向量`
        self.lstm = nn.LSTM(
            input_size=num_future_features + hidden_dim,  # 这是关键变化！
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout_rate if num_layers > 1 else 0,
        )
        self.fc = nn.Linear(hidden_dim, num_target_features)

    def forward(self, x_future_step, hidden, cell, encoder_outputs):
        """
        注意：这里的解码器是一步一步进行的
        :param x_future_step: 单个时间步的未来输入, 形状 [batch_size, 1, num_future_features]
        :param hidden: 解码器上一个时间步的隐藏状态
        :param cell: 解码器上一个时间步的细胞状态
        :param encoder_outputs: 编码器所有时间步的输出
        :return: 当前步的预测值，新的隐藏状态，新的细胞状态
        """
        # 1. 计算注意力权重
        # attention_weights 形状: [batch_size, 1, PAST_SEQ_LEN]
        attention_weights = self.attention(hidden, encoder_outputs).unsqueeze(1)

        # 2. 计算上下文向量
        # context_vector 形状: [batch_size, 1, hidden_dim]
        context_vector = torch.bmm(attention_weights, encoder_outputs)

        # 3. 准备LSTM的输入
        # 将当前时间步的未来输入和上下文向量拼接
        # lstm_input 形状: [batch_size, 1, num_future_features + hidden_dim]
        lstm_input = torch.cat((x_future_step, context_vector), dim=2)

        # 4. 通过LSTM传递
        lstm_output, (hidden, cell) = self.lstm(lstm_input, (hidden, cell))

        # 5. 预测
        # prediction 形状: [batch_size, num_target_features]
        prediction = self.fc(lstm_output.squeeze(1))

        return prediction, hidden, cell


class Attention(nn.Module):
    def __init__(self, hidden_dim):
        super(Attention, self).__init__()
        # 用于计算对齐分数（alignment score）的线性层
        self.attn = nn.Linear(hidden_dim * 2, hidden_dim)
        self.v = nn.Parameter(torch.rand(hidden_dim))

    def forward(self, hidden, encoder_outputs):
        """
        :param hidden: 解码器当前（或上一个）时间步的隐藏状态, 形状 [num_layers, batch_size, hidden_dim]
                       我们通常只使用最顶层的 hidden state。
        :param encoder_outputs: 编码器所有时间步的输出, 形状 [batch_size, PAST_SEQ_LEN, hidden_dim]
        :return: 注意力权重, 形状 [batch_size, PAST_SEQ_LEN]
        """
        batch_size = encoder_outputs.size(0)
        past_seq_len = encoder_outputs.size(1)

        # 我们只取顶层的隐藏状态来进行注意力计算
        hidden = (
            hidden[-1].unsqueeze(1).repeat(1, past_seq_len, 1)
        )  # [batch_size, PAST_SEQ_LEN, hidden_dim]

        # 将解码器隐藏状态和编码器输出拼接
        # concat_out 形状: [batch_size, PAST_SEQ_LEN, hidden_dim * 2]
        concat_out = torch.cat((hidden, encoder_outputs), dim=2)

        # energy 形状: [batch_size, PAST_SEQ_LEN, hidden_dim]
        energy = torch.tanh(self.attn(concat_out))

        # energy 形状: [batch_size, hidden_dim, PAST_SEQ_LEN]
        energy = energy.transpose(1, 2)

        # v 形状: [hidden_dim, 1]
        v = self.v.repeat(batch_size, 1).unsqueeze(1)  # [batch_size, 1, hidden_dim]

        # attention_scores 形状: [batch_size, 1, PAST_SEQ_LEN]
        attention_scores = torch.bmm(v, energy).squeeze(1)

        # 返回经过softmax的权重
        return F.softmax(attention_scores, dim=1)


class Seq2SeqWithAttention(nn.Module):
    def __init__(self, encoder, decoder):
        super(Seq2SeqWithAttention, self).__init__()
        self.encoder = encoder
        self.decoder = decoder

    def forward(self, x_past, x_future):
        """
        :param x_past: 过去的数据，形状为 [batch_size, PAST_SEQ_LEN, num_past_features]
        :param x_future: 未来的已知数据，形状为 [batch_size, FUTURE_SEQ_LEN, num_future_features]
        :return: 最终的预测结果
        """
        batch_size = x_past.size(0)
        future_seq_len = x_future.size(1)
        num_target_features = self.decoder.fc.out_features
        device = x_past.device

        # 创建一个空的tensor来存储所有时间步的预测
        predictions = torch.zeros(batch_size, future_seq_len, num_target_features).to(
            device
        )

        # 1. 运行编码器
        encoder_outputs, hidden, cell = self.encoder(x_past)

        # 2. 循环运行解码器
        # 按时间步-对未来的输入进行迭代
        for t in range(future_seq_len):
            # 获取当前时间步的输入
            x_future_step = x_future[:, t, :].unsqueeze(
                1
            )  # 形状: [batch_size, 1, num_future_features]

            # 进行一步解码
            prediction, hidden, cell = self.decoder(
                x_future_step, hidden, cell, encoder_outputs
            )

            # 存储当前步的预测结果
            predictions[:, t, :] = prediction

        return predictions
