import torch
import torch.nn as nn
import math


class PositionalEncoding(nn.Module):
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)

        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(
            torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model)
        )
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        self.register_buffer("pe", pe)  # 将pe注册为buffer，这样它不会被视为模型参数

    def forward(self, x):
        """
        x: 形状为 [seq_len, batch_size, d_model]
        """
        x = x + self.pe[: x.size(0), :]  # type: ignore
        return self.dropout(x)


class TransformerModel(nn.Module):
    def __init__(
        self,
        num_past_features,
        num_future_features,
        num_target_features,
        d_model,
        n_heads,
        num_encoder_layers,
        num_decoder_layers,
        dim_feedforward,
        dropout_rate,
    ):
        super(TransformerModel, self).__init__()
        self.d_model = d_model

        # --- 1. 输入线性映射层 ---
        # 将输入特征映射到模型维度 D_MODEL
        self.encoder_input_fc = nn.Linear(num_past_features, d_model)
        self.decoder_input_fc = nn.Linear(num_future_features, d_model)

        # --- 2. 位置编码 ---
        self.pos_encoder = PositionalEncoding(d_model, dropout_rate)

        # --- 3. 核心Transformer模块 ---
        self.transformer = nn.Transformer(
            d_model=d_model,
            nhead=n_heads,
            num_encoder_layers=num_encoder_layers,
            num_decoder_layers=num_decoder_layers,
            dim_feedforward=dim_feedforward,
            dropout=dropout_rate,
            batch_first=True,  # 非常重要！使用批次优先的形状
        )

        # --- 4. 输出线性映射层 ---
        # 将Transformer解码器的输出映射到目标特征维度
        self.output_fc = nn.Linear(d_model, num_target_features)

    def _generate_square_subsequent_mask(self, sz):
        """生成解码器自注意力所需的因果掩码"""
        mask = (torch.triu(torch.ones(sz, sz)) == 1).transpose(0, 1)
        mask = (
            mask.float()
            .masked_fill(mask == 0, float("-inf"))
            .masked_fill(mask == 1, float(0.0))
        )
        return mask

    def forward(self, x_past, x_future):
        """
        :param x_past: 过去的数据，形状 [batch_size, PAST_SEQ_LEN, num_past_features]
        :param x_future: 未来的已知数据，形状 [batch_size, FUTURE_SEQ_LEN, num_future_features]
        :return: 预测结果，形状 [batch_size, FUTURE_SEQ_LEN, num_target_features]
        """
        device = x_past.device

        # --- 准备编码器输入 ---
        # 1. 映射到D_MODEL维度: [batch_size, PAST_SEQ_LEN, d_model]
        encoder_input = self.encoder_input_fc(x_past)
        # 2. 添加位置编码
        encoder_input = self.pos_encoder(encoder_input.transpose(0, 1)).transpose(
            0, 1
        )  # [batch_size, PAST_SEQ_LEN, d_model]

        # --- 准备解码器输入 ---
        # 1. 映射到D_MODEL维度: [batch_size, FUTURE_SEQ_LEN, d_model]
        decoder_input = self.decoder_input_fc(x_future)
        # 2. 添加位置编码
        decoder_input = self.pos_encoder(decoder_input.transpose(0, 1)).transpose(
            0, 1
        )  # [batch_size, FUTURE_SEQ_LEN, d_model]

        # --- 创建掩码 ---
        # 解码器自注意力掩码，防止看到未来的信息
        tgt_mask = self._generate_square_subsequent_mask(x_future.size(1)).to(device)

        # --- 通过Transformer ---
        transformer_out = self.transformer(
            src=encoder_input, tgt=decoder_input, tgt_mask=tgt_mask
        )  # 形状: [batch_size, FUTURE_SEQ_LEN, d_model]

        # --- 输出层 ---
        predictions = self.output_fc(
            transformer_out
        )  # 形状: [batch_size, FUTURE_SEQ_LEN, num_target_features]

        return predictions
