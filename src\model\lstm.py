import os
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import lightning as L
from torch.optim.lr_scheduler import ReduceLROnPlateau, CosineAnnealingLR
from typing import Dict, List, Tuple, Optional, Union, Any
import math


class MultiHeadAttention(nn.Module):
    """多头注意力机制"""

    def __init__(self, d_model: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        assert d_model % num_heads == 0

        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads

        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)

        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        batch_size, seq_len, d_model = x.size()

        # 残差连接
        residual = x

        # 计算Q, K, V
        Q = (
            self.w_q(x)
            .view(batch_size, seq_len, self.num_heads, self.d_k)
            .transpose(1, 2)
        )
        K = (
            self.w_k(x)
            .view(batch_size, seq_len, self.num_heads, self.d_k)
            .transpose(1, 2)
        )
        V = (
            self.w_v(x)
            .view(batch_size, seq_len, self.num_heads, self.d_k)
            .transpose(1, 2)
        )

        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)

        # 应用注意力权重
        context = torch.matmul(attention_weights, V)
        context = (
            context.transpose(1, 2).contiguous().view(batch_size, seq_len, d_model)
        )

        # 输出投影
        output = self.w_o(context)

        # 残差连接和层归一化
        output = self.layer_norm(output + residual)

        return output


class LSTMModel(nn.Module):
    """
    LSTM模型基础架构
    """

    def __init__(
        self,
        input_size: int,
        hidden_size: int,
        num_layers: int,
        output_size: int,
        dropout: float = 0.1,
        bidirectional: bool = False,
        use_attention: bool = True,
        use_residual: bool = True,
    ):
        """
        LSTM模型初始化

        Args:
            input_size: 输入特征的维度
            hidden_size: LSTM隐藏层的维度
            num_layers: LSTM层数
            output_size: 输出特征的维度
            dropout: dropout比率，默认为0.1
            bidirectional: 是否使用双向LSTM，默认为False
            use_attention: 是否使用注意力机制，默认为True
            use_residual: 是否使用残差连接，默认为True
        """
        super().__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.output_size = output_size
        self.dropout = dropout
        self.bidirectional = bidirectional
        self.use_attention = use_attention
        self.use_residual = use_residual

        # 定义LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=bidirectional,
        )

        # 如果是双向LSTM，则隐藏层维度会翻倍
        lstm_output_size = hidden_size * 2 if bidirectional else hidden_size

        # 注意力机制
        if self.use_attention:
            self.attention = MultiHeadAttention(
                lstm_output_size, num_heads=8, dropout=dropout
            )

        # 残差连接的投影层（如果输入和输出维度不同）
        if self.use_residual and input_size != lstm_output_size:
            self.residual_projection = nn.Linear(input_size, lstm_output_size)
        else:
            self.residual_projection = None

        # 层归一化
        self.layer_norm = nn.LayerNorm(lstm_output_size)

        # 定义输出层
        self.fc = nn.Sequential(
            nn.Linear(lstm_output_size, lstm_output_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(lstm_output_size // 2, output_size),
        )

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """
        初始化模型权重
        """
        for name, param in self.lstm.named_parameters():
            if "weight" in name:
                nn.init.xavier_uniform_(param)
            elif "bias" in name:
                nn.init.zeros_(param)

        for name, param in self.fc.named_parameters():
            if "weight" in name:
                nn.init.xavier_uniform_(param)
            elif "bias" in name:
                nn.init.zeros_(param)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入张量，形状为 [batch_size, seq_len, input_size]

        Returns:
            输出张量，形状为 [batch_size, output_size]
        """
        # LSTM forward: x shape: [batch_size, seq_len, input_size]
        lstm_out, _ = self.lstm(x)

        # 残差连接
        if self.use_residual:
            if self.residual_projection is not None:
                # 如果输入和LSTM输出维度不同，需要投影
                residual = self.residual_projection(x)
            else:
                # 如果维度相同，直接使用输入
                residual = x

            # 添加残差连接
            lstm_out = lstm_out + residual

        # 应用注意力机制
        if self.use_attention:
            lstm_out = self.attention(lstm_out)

        # 层归一化
        lstm_out = self.layer_norm(lstm_out)

        # 我们只需要序列的最后一个时间步的输出
        last_output = lstm_out[:, -1, :]

        # 通过全连接层
        output = self.fc(last_output)

        return output


class LitLSTM(L.LightningModule):
    """
    PyTorch Lightning LSTM模型
    """

    def __init__(
        self,
        input_size: int,
        hidden_size: int = 128,
        num_layers: int = 2,
        output_size: int = 1,
        dropout: float = 0.1,
        bidirectional: bool = False,
        learning_rate: float = 1e-3,
        weight_decay: float = 1e-5,
        use_attention: bool = True,
        use_residual: bool = True,
        loss_type: str = "mse",
        scheduler_type: str = "plateau",
    ):
        """
        初始化Lightning LSTM模型

        Args:
            input_size: 输入特征的维度
            hidden_size: LSTM隐藏层的维度，默认为128
            num_layers: LSTM的层数，默认为2
            output_size: 输出特征的维度，默认为1
            dropout: dropout比率，默认为0.1
            bidirectional: 是否使用双向LSTM，默认为False
            learning_rate: 学习率，默认为1e-3
            weight_decay: 权重衰减系数，默认为1e-5
            use_attention: 是否使用注意力机制，默认为True
            use_residual: 是否使用残差连接，默认为True
            loss_type: 损失函数类型，默认为"mse"
            scheduler_type: 学习率调度器类型，默认为"plateau"
        """
        super().__init__()
        self.save_hyperparameters()

        # 创建LSTM模型
        self.model = LSTMModel(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            output_size=output_size,
            dropout=dropout,
            bidirectional=bidirectional,
            use_attention=use_attention,
            use_residual=use_residual,
        )

        # 定义损失函数
        if loss_type == "mse":
            self.criterion = nn.MSELoss()
        elif loss_type == "huber":
            self.criterion = nn.HuberLoss()
        elif loss_type == "smooth_l1":
            self.criterion = nn.SmoothL1Loss()
        else:
            self.criterion = nn.MSELoss()  # 默认使用MSE

        # 记录训练参数
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入张量，形状为 [batch_size, seq_len, input_size]

        Returns:
            输出张量，形状为 [batch_size, output_size]
        """
        return self.model(x)

    def training_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        训练步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含损失的字典
        """
        x, y = batch

        # 对于多步预测，我们只关心输入序列
        y_hat = self(x)

        # 这里假设y的最后一个维度是目标值
        # 在多步预测情况下，y的形状可能是[batch_size, horizon, output_size]
        # 在单步预测情况下，y的形状可能是[batch_size, output_size]
        if len(y.shape) == 3:
            # 多步预测：只取最后一个时间步
            y = y[:, -1, :]

        loss = self.criterion(y_hat, y)
        self.log("train_loss", loss, prog_bar=True, on_step=True, on_epoch=True)

        return {"loss": loss}

    def validation_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        验证步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含验证损失和指标的字典
        """
        x, y = batch
        y_hat = self(x)

        if len(y.shape) == 3:
            y = y[:, -1, :]

        val_loss = self.criterion(y_hat, y)

        # 计算MAE和RMSE
        mae = F.l1_loss(y_hat, y)
        rmse = torch.sqrt(self.criterion(y_hat, y))

        # 记录指标
        self.log("val_loss", val_loss, prog_bar=True)
        self.log("val_mae", mae, prog_bar=True)
        self.log("val_rmse", rmse, prog_bar=True)

        return {"val_loss": val_loss, "val_mae": mae, "val_rmse": rmse}

    def test_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        测试步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含测试损失和指标的字典
        """
        x, y = batch
        y_hat = self(x)

        if len(y.shape) == 3:
            y = y[:, -1, :]

        test_loss = self.criterion(y_hat, y)

        # 计算MAE和RMSE
        mae = F.l1_loss(y_hat, y)
        rmse = torch.sqrt(self.criterion(y_hat, y))

        # 记录指标
        self.log("test_loss", test_loss)
        self.log("test_mae", mae)
        self.log("test_rmse", rmse)

        return {"test_loss": test_loss, "test_mae": mae, "test_rmse": rmse}

    def predict_step(self, batch: torch.Tensor, batch_idx: int) -> torch.Tensor:
        """
        预测步骤

        Args:
            batch: 输入张量
            batch_idx: 批次索引

        Returns:
            预测结果
        """
        if isinstance(batch, tuple):
            x = batch[0]
        else:
            x = batch

        return self(x)

    def configure_optimizers(self) -> Dict[str, Any]:
        """
        配置优化器和学习率调度器

        Returns:
            包含优化器和学习率调度器的字典
        """
        # 使用AdamW优化器，通常比Adam更好
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.hparams.get("learning_rate", 1e-3),
            weight_decay=self.hparams.get("weight_decay", 1e-5),
        )

        # 根据配置选择调度器
        if self.hparams.get("scheduler_type", "plateau") == "plateau":
            scheduler = {
                "scheduler": ReduceLROnPlateau(
                    optimizer,
                    mode="min",
                    factor=0.5,
                    patience=5,
                    min_lr=1e-6,
                ),
                "monitor": "val_loss",
                "interval": "epoch",
                "frequency": 1,
            }
        elif self.hparams.get("scheduler_type", "plateau") == "cosine":
            scheduler = {
                "scheduler": CosineAnnealingLR(
                    optimizer,
                    T_max=50,  # 余弦退火的周期
                    eta_min=1e-6,  # 最小学习率
                ),
                "interval": "epoch",
                "frequency": 1,
            }
        else:
            # 默认使用plateau调度器
            scheduler = {
                "scheduler": ReduceLROnPlateau(
                    optimizer,
                    mode="min",
                    factor=0.5,
                    patience=5,
                    min_lr=1e-6,
                ),
                "monitor": "val_loss",
                "interval": "epoch",
                "frequency": 1,
            }

        return {"optimizer": optimizer, "lr_scheduler": scheduler}
