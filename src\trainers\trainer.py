import os
import lightning as L

from config.configs import TrainConfig
from lightning.pytorch.callbacks import (
    ModelCheckpoint,
    EarlyStopping,
    LearningRateMonitor,
)
from lightning.pytorch.loggers import TensorBoardLogger


def train_model(
    model_name: str,
    work_dir: str,
    model: L.LightningModule,
    data_loader: L.LightningDataModule,
    train_config: TrainConfig,
):
    train_loader = data_loader.train_dataloader()
    val_loader = data_loader.val_dataloader()
    test_loader = data_loader.test_dataloader()

    # 设置回调
    checkpoint_callback = get_checkpoint_callback(model_name)
    early_stopping = get_early_stopping_callback(train_config.early_stopping_patience)
    lr_monitor = get_lr_monitor()
    # 设置日志记录器
    tensorboard_logger = get_tensorboard_logger(model_name)

    # 创建训练器
    trainer = L.Trainer(
        max_epochs=train_config.epochs,
        callbacks=[checkpoint_callback, early_stopping, lr_monitor],
        logger=tensorboard_logger,
        accelerator=train_config.accelerator,
        devices=train_config.devices,
        gradient_clip_val=train_config.gradient_clip_val,
        precision="16-mixed",  # 启用混合精度训练
        deterministic=True,  # 确保结果可重现
        enable_progress_bar=True,  # 显示进度条
    )

    # 训练模型
    trainer.fit(
        model=model,
        train_dataloaders=train_loader,
        val_dataloaders=val_loader,
    )

    # 评估模型
    trainer.test(model=model, dataloaders=test_loader)

    # 保存模型
    save_path = os.path.join(work_dir, f"{model_name.lower()}.ckpt")
    trainer.save_checkpoint(save_path)


def get_checkpoint_callback(model_name: str):
    return ModelCheckpoint(
        monitor="val_loss",
        filename=f"{model_name.lower()}-{{epoch:02d}}-{{val_loss:.4f}}",
        save_top_k=3,
        mode="min",
    )


def get_early_stopping_callback(patience: int = 10):
    return EarlyStopping(
        monitor="val_loss",
        patience=patience,
        mode="min",
    )


def get_lr_monitor():
    return LearningRateMonitor(logging_interval="epoch")


def get_tensorboard_logger(model_name: str):
    return TensorBoardLogger("logs", name=model_name.lower())
