#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版CSV文件导入InfluxDB脚本

功能：
- 读取CSV文件并导入到InfluxDB数据库
- 支持基本的数据类型处理
- 简单的时间戳解析
- 可配置的连接参数

使用方法：
1. 修改下面的配置参数
2. 直接运行脚本: python import_csv.py

作者: Augment Agent
创建时间: 2025-07-10
"""

import pandas as pd
import numpy as np
from influxdb import InfluxDBClient
from pathlib import Path
from datetime import datetime
import warnings

# 忽略警告
warnings.filterwarnings("ignore")

# ==================== 配置参数 ====================
# CSV文件路径
CSV_PATH = "D:/CodeSpace/Data-Driven-Model/AnnealingFurnace/strip-temp-predict/data/test-lstm/20250725/data/data_resampled.csv"

# 时间戳列名
TIME_COL = "INDEX"

# 标签列（这些列将作为InfluxDB的标签，用于索引和查询优化）
TAG_COLUMNS = []  # 例如: ["sensor_id", "location", "device_type"]

# InfluxDB连接参数
HOST = "***********"
PORT = 8086
USERNAME = "root"
PASSWORD = "123456"
DATABASE = "csv_view"
MEASUREMENT = "data_indexed"

# 处理参数
CHUNK_SIZE = 1000  # 分块处理大小
BATCH_SIZE = 500  # 批量写入大小
# ================================================


def parse_timestamp(df, time_col):
    """
    解析时间戳列

    Args:
        df: DataFrame
        time_col: 时间戳列名

    Returns:
        处理后的DataFrame
    """
    if time_col not in df.columns:
        raise ValueError(f"时间戳列 '{time_col}' 不存在")

    # 如果已经是datetime类型，直接返回
    if pd.api.types.is_datetime64_any_dtype(df[time_col]):
        return df

    try:
        # 尝试自动解析时间戳
        df[time_col] = pd.to_datetime(df[time_col], format="mixed")
        print(f"✅ 成功解析时间戳列: {time_col}")
        return df
    except Exception as e:
        print(f"❌ 时间戳解析失败: {e}")
        raise


def convert_to_influx_points(df, measurement, time_col, tag_columns):
    """
    将DataFrame转换为InfluxDB数据点格式

    Args:
        df: DataFrame
        measurement: 测量名称
        time_col: 时间戳列名
        tag_columns: 标签列列表

    Returns:
        InfluxDB数据点列表
    """
    points = []

    for _, row in df.iterrows():
        # 跳过时间戳为空的行
        if pd.isna(row[time_col]):
            continue

        # 构建数据点
        point = {
            "measurement": measurement,
            "time": row[time_col].isoformat(),
            "fields": {},
            "tags": {},
        }

        # 添加标签
        for tag_col in tag_columns:
            if tag_col in row and pd.notna(row[tag_col]):
                point["tags"][tag_col] = str(row[tag_col])

        # 添加字段（除了时间戳和标签列）
        for col in df.columns:
            if col == time_col or col in tag_columns:
                continue

            value = row[col]
            if pd.notna(value):
                # 处理不同数据类型
                if isinstance(value, (bool, np.bool_)):
                    point["fields"][col] = bool(value)
                elif isinstance(value, (int, np.integer)):
                    point["fields"][col] = int(value)
                elif isinstance(value, (float, np.floating)):
                    if not np.isnan(value) and not np.isinf(value):
                        point["fields"][col] = float(value)
                else:
                    point["fields"][col] = str(value)

        # 只有当fields不为空时才添加数据点
        if point["fields"]:
            points.append(point)

    return points


def csv_to_influxdb():
    """
    主函数：将CSV文件导入到InfluxDB
    """
    print("=" * 60)
    print("CSV文件导入InfluxDB")
    print("=" * 60)

    # 检查CSV文件是否存在
    csv_path = Path(CSV_PATH)
    if not csv_path.exists():
        print(f"❌ CSV文件不存在: {csv_path}")
        return False

    print(f"📁 CSV文件: {csv_path}")
    print(f"🏷️  测量名称: {MEASUREMENT}")
    print(f"⏰ 时间戳列: {TIME_COL}")
    print(f"🔖 标签列: {TAG_COLUMNS}")

    try:
        # 连接InfluxDB
        print(f"\n🔗 连接InfluxDB: {HOST}:{PORT}")
        client = InfluxDBClient(
            host=HOST,
            port=PORT,
            username=USERNAME,
            password=PASSWORD,
            database=DATABASE,
        )

        # 测试连接
        client.ping()
        print("✅ InfluxDB连接成功")

        # 创建数据库（如果不存在）
        databases = client.get_list_database()
        db_names = [db["name"] for db in databases]
        if DATABASE not in db_names:
            client.create_database(DATABASE)
            print(f"✅ 创建数据库: {DATABASE}")
        else:
            print(f"✅ 数据库已存在: {DATABASE}")

        # 清空已存在的measurement数据（可选）
        try:
            # 直接尝试删除measurement的所有数据
            # 如果measurement不存在，这个命令会静默失败，不会报错
            client.query(f'DROP SERIES FROM "{MEASUREMENT}"')
            print(f"🗑️  清空measurement '{MEASUREMENT}' 的历史数据")

        except Exception as e:
            # 如果删除失败（比如measurement不存在），继续执行
            print(f"ℹ️  无法清空measurement数据（可能不存在）: {e}")
            print("继续执行导入...")

        # 获取文件大小
        file_size = csv_path.stat().st_size / 1024 / 1024
        print(f"📊 文件大小: {file_size:.2f} MB")

        total_points = 0
        chunk_count = 0

        # 分块读取和处理CSV文件
        print(f"\n📖 开始读取CSV文件（分块大小: {CHUNK_SIZE}）...")

        for chunk_df in pd.read_csv(csv_path, chunksize=CHUNK_SIZE):
            chunk_count += 1
            chunk_rows = len(chunk_df)

            print(f"📦 处理第 {chunk_count} 块数据，包含 {chunk_rows} 行")

            try:
                # 解析时间戳
                chunk_df = parse_timestamp(chunk_df, TIME_COL)

                # 转换为InfluxDB数据点
                points = convert_to_influx_points(
                    chunk_df, MEASUREMENT, TIME_COL, TAG_COLUMNS
                )

                if not points:
                    print("⚠️  该块没有有效数据点，跳过")
                    continue

                # 分批写入数据
                points_written = 0
                for i in range(0, len(points), BATCH_SIZE):
                    batch = points[i : i + BATCH_SIZE]
                    client.write_points(batch)
                    points_written += len(batch)

                total_points += points_written
                print(f"✅ 第 {chunk_count} 块写入成功，写入 {points_written} 个数据点")

            except Exception as e:
                print(f"❌ 处理第 {chunk_count} 块数据时出错: {e}")
                continue

        # 关闭连接
        client.close()

        print(f"\n🎉 导入完成！")
        print(f"📊 总共写入 {total_points} 个数据点")
        print(f"📦 处理了 {chunk_count} 个数据块")

        return True

    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False


if __name__ == "__main__":
    success = csv_to_influxdb()
    if success:
        print("\n✅ 脚本执行成功")
    else:
        print("\n❌ 脚本执行失败")
