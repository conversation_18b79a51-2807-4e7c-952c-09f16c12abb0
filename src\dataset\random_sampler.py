import logging
import random
from torch.utils.data import Sampler


class RandomSubsetSampler(Sampler):
    """
    随机采样数据集的一个子集，不放回。

    参数:
        dataset_len (Dataset): 要从中采样的数据集。
        num_samples (int): 每个 epoch 要采样的样本数量。
    """

    def __init__(self, dataset_len: int, num_samples):
        self.dataset_len = dataset_len
        self.num_samples = num_samples
        self.logger = logging.getLogger(self.__class__.__name__)

        if self.num_samples <= 0:
            self.logger.warning(
                f"num_samples {self.num_samples} <= 0, 设置为数据集长度 {self.dataset_len}"
            )
            self.num_samples = self.dataset_len

        # 确保采样数量不超过数据集大小
        if self.num_samples > self.dataset_len:
            self.logger.warning(
                f"num_samples {self.num_samples} > 数据集长度 {self.dataset_len}，设置为数据集长度"
            )
            self.num_samples = self.dataset_len

    def __iter__(self):
        # 生成所有可能的索引
        all_indices = list(range(self.dataset_len))
        # 随机打乱索引
        random.shuffle(all_indices)
        # 取前 num_samples 个索引
        subset_indices = all_indices[: self.num_samples]
        return iter(subset_indices)

    def __len__(self):
        return self.num_samples
